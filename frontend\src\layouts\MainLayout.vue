<template>
  <el-container class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="main-header">
      <div class="header-left">
        <el-button
          class="menu-toggle"
          :icon="Fold"
          @click="toggleSidebar"
          v-if="isMobile"
        />
        <div class="logo">
          <div class="logo-icon">
            <el-icon :size="28"><ShoppingBag /></el-icon>
          </div>
          <span class="logo-text">商品租赁系统</span>
        </div>
      </div>
      
      <div class="header-right">
        <!-- 用户菜单 -->
        <el-dropdown @command="handleCommand">
          <div class="user-info">
            <el-avatar :size="32" :src="userAvatar" />
            <span class="username">{{ authStore.user?.username }}</span>
            <el-icon><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人资料
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="main-sidebar">
        <div class="sidebar-content">
          <!-- 用户信息 -->
          <div class="user-profile">
            <el-avatar :size="60" :src="userAvatar" />
            <h4>{{ authStore.user?.username }}</h4>
            <p>{{ authStore.user?.role === 'ADMIN' ? '管理员' : '用户' }}</p>
          </div>
          
          <!-- 导航菜单 -->
          <el-menu
            :default-active="activeMenu"
            class="sidebar-menu"
            router
            :collapse="isCollapsed"
          >
            <el-menu-item index="/dashboard">
              <el-icon><Odometer /></el-icon>
              <template #title>控制面板</template>
            </el-menu-item>
            
            <el-menu-item index="/items">
              <el-icon><Box /></el-icon>
              <template #title>商品管理</template>
            </el-menu-item>
            
            <el-menu-item index="/orders">
              <el-icon><ShoppingCart /></el-icon>
              <template #title>租赁订单</template>
            </el-menu-item>
            
            <el-menu-item 
              index="/users" 
              v-if="authStore.isAdmin"
            >
              <el-icon><UserFilled /></el-icon>
              <template #title>用户管理</template>
            </el-menu-item>
            
            <el-menu-item index="/statistics">
              <el-icon><TrendCharts /></el-icon>
              <template #title>数据统计</template>
            </el-menu-item>
            
            <el-menu-item 
              index="/settings" 
              v-if="authStore.isAdmin"
            >
              <el-icon><Setting /></el-icon>
              <template #title>系统设置</template>
            </el-menu-item>
          </el-menu>
          
          <!-- 系统状态 -->
          <div class="system-status" v-if="!isCollapsed">
            <h6>系统状态</h6>
            <div class="status-item">
              <span>在线用户</span>
              <span class="status-value">24</span>
            </div>
            <div class="status-item">
              <span>今日订单</span>
              <span class="status-value">8</span>
            </div>
            <div class="status-item">
              <span>商品总数</span>
              <span class="status-value">42</span>
            </div>
          </div>
        </div>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Fold,
  Expand,
  ShoppingBag,
  ArrowDown,
  User,
  Setting,
  SwitchButton,
  Odometer,
  Box,
  ShoppingCart,
  UserFilled,
  TrendCharts
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式状态
const isMobile = ref(false)
const isCollapsed = ref(false)

// 计算属性
const activeMenu = computed(() => route.path)
const sidebarWidth = computed(() => {
  if (isMobile.value) {
    return isCollapsed.value ? '0px' : '250px'
  }
  return isCollapsed.value ? '64px' : '250px'
})

const userAvatar = computed(() => {
  return `https://ui-avatars.com/api/?name=${authStore.user?.username}&background=random`
})

// 检查屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
  if (isMobile.value) {
    isCollapsed.value = true
  }
}

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 处理用户菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中')
      break
    case 'settings':
      if (authStore.isAdmin) {
        router.push('/settings')
      } else {
        ElMessage.info('设置功能开发中')
      }
      break
    case 'logout':
      ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        authStore.logout()
        router.push('/login')
      })
      break
  }
}

onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style lang="scss" scoped>
.main-layout {
  height: 100vh;
}

.main-header {
  background: var(--gradient-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: var(--box-shadow-lg);
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left {
  display: flex;
  align-items: center;

  .menu-toggle {
    margin-right: 16px;
    background: transparent;
    border: none;
    color: white;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }

  .logo {
    display: flex;
    align-items: center;
    font-size: 1.3rem;
    font-weight: 700;

    .logo-icon {
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      backdrop-filter: blur(10px);
      transition: var(--transition);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
      }
    }

    .logo-text {
      background: linear-gradient(45deg, rgba(255,255,255,1), rgba(255,255,255,0.8));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;

      @media (max-width: 480px) {
        display: none;
      }
    }
  }
}

.header-right {
  .user-info {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: background-color 0.3s;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .username {
      margin: 0 8px;
      font-weight: 500;

      @media (max-width: 480px) {
        display: none;
      }
    }
  }
}

.main-sidebar {
  background: var(--gradient-card);
  box-shadow: var(--box-shadow-lg);
  transition: var(--transition);
  overflow: hidden;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.user-profile {
  text-align: center;
  padding: 24px 16px;
  border-bottom: 1px solid #f0f0f0;

  h4 {
    margin: 12px 0 4px;
    color: var(--dark-color);
    font-weight: 600;
  }

  p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
  }
}

.sidebar-menu {
  flex: 1;
  border: none;

  .el-menu-item {
    height: 48px;
    line-height: 48px;
    margin: 4px 8px;
    border-radius: 8px;

    &:hover {
      background-color: rgba(67, 97, 238, 0.1);
      color: var(--primary-color);
    }

    &.is-active {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;

      .el-icon {
        color: white;
      }
    }
  }
}

.system-status {
  padding: 16px;
  margin: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;

  h6 {
    margin: 0 0 12px;
    color: var(--dark-color);
    font-weight: 600;
  }

  .status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;

    &:last-child {
      margin-bottom: 0;
    }

    .status-value {
      font-weight: 600;
      color: var(--primary-color);
    }
  }
}

.main-content {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 32px;
  overflow-y: auto;
  min-height: calc(100vh - 60px);
}

// 响应式设计
@media (max-width: 768px) {
  .main-sidebar {
    position: fixed;
    top: 60px;
    left: 0;
    height: calc(100vh - 60px);
    z-index: 999;
  }

  .user-profile {
    padding: 16px;
  }

  .system-status {
    margin: 8px;
    padding: 12px;
  }
}
</style>
