{"version": 3, "file": "popconfirm.mjs", "sources": ["../../../../../../packages/components/popconfirm/src/popconfirm.ts"], "sourcesContent": ["import { buttonTypes } from '@element-plus/components/button'\nimport { QuestionFilled } from '@element-plus/icons-vue'\nimport { buildProps, iconPropType } from '@element-plus/utils'\nimport { useTooltipContentProps } from '@element-plus/components/tooltip'\nimport type { ExtractPropTypes } from 'vue'\nimport type Popconfirm from './popconfirm.vue'\n\nexport const popconfirmProps = buildProps({\n  /**\n   * @description Title\n   */\n  title: String,\n  /**\n   * @description Confirm button text\n   */\n  confirmButtonText: String,\n  /**\n   * @description Cancel button text\n   */\n  cancelButtonText: String,\n  /**\n   * @description Confirm button type\n   */\n  confirmButtonType: {\n    type: String,\n    values: buttonTypes,\n    default: 'primary',\n  },\n  /**\n   * @description Cancel button type\n   */\n  cancelButtonType: {\n    type: String,\n    values: buttonTypes,\n    default: 'text',\n  },\n  /**\n   * @description Icon Component\n   */\n  icon: {\n    type: iconPropType,\n    default: () => QuestionFilled,\n  },\n  /**\n   * @description Icon color\n   */\n  iconColor: {\n    type: String,\n    default: '#f90',\n  },\n  /**\n   * @description is hide Icon\n   */\n  hideIcon: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description delay of disappear, in millisecond\n   */\n  hideAfter: {\n    type: Number,\n    default: 200,\n  },\n  /**\n   * @description whether popconfirm is teleported to the body\n   */\n  teleported: useTooltipContentProps.teleported,\n  /**\n   * @description when popconfirm inactive and `persistent` is `false` , popconfirm will be destroyed\n   */\n  persistent: useTooltipContentProps.persistent,\n  /**\n   * @description popconfirm width, min width 150px\n   */\n  width: {\n    type: [String, Number],\n    default: 150,\n  },\n} as const)\n\nexport const popconfirmEmits = {\n  /**\n   * @description triggers when click confirm button\n   */\n  confirm: (e: MouseEvent) => e instanceof MouseEvent,\n  /**\n   * @description triggers when click cancel button\n   */\n  cancel: (e: MouseEvent) => e instanceof MouseEvent,\n}\n\nexport type PopconfirmEmits = typeof popconfirmEmits\n\nexport type PopconfirmProps = ExtractPropTypes<typeof popconfirmProps>\n\nexport type PopconfirmInstance = InstanceType<typeof Popconfirm> & unknown\n"], "names": [], "mappings": ";;;;;;AAIY,MAAC,eAAe,GAAG,UAAU,CAAC;AAC1C,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,iBAAiB,EAAE,MAAM;AAC3B,EAAE,gBAAgB,EAAE,MAAM;AAC1B,EAAE,iBAAiB,EAAE;AACrB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,WAAW;AACvB,IAAI,OAAO,EAAE,SAAS;AACtB,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,WAAW;AACvB,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,OAAO,EAAE,MAAM,cAAc;AACjC,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,UAAU,EAAE,sBAAsB,CAAC,UAAU;AAC/C,EAAE,UAAU,EAAE,sBAAsB,CAAC,UAAU;AAC/C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,eAAe,GAAG;AAC/B,EAAE,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,YAAY,UAAU;AACzC,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,YAAY,UAAU;AACxC;;;;"}