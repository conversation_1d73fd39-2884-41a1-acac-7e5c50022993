{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/steps/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\n\nimport Steps from './src/steps.vue'\nimport Step from './src/item.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSteps: SFCWithInstall<typeof Steps> & {\n  Step: typeof Step\n} = withInstall(Steps, {\n  Step,\n})\nexport default ElSteps\nexport const ElStep: SFCWithInstall<typeof Step> = withNoopInstall(Step)\n\nexport * from './src/item'\nexport * from './src/steps'\nexport * from './src/tokens'\n"], "names": [], "mappings": ";;;;;;;AAGY,MAAC,OAAO,GAAG,WAAW,CAAC,KAAK,EAAE;AAC1C,EAAE,IAAI;AACN,CAAC,EAAE;AAES,MAAC,MAAM,GAAG,eAAe,CAAC,IAAI;;;;"}