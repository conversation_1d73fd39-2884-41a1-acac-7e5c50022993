<template>
  <div class="orders-view">
    <div class="page-header">
      <div class="header-left">
        <h1>租赁订单</h1>
        <p>管理所有租赁订单</p>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          :icon="Plus"
          @click="showCreateDialog = true"
        >
          创建订单
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderId"
            placeholder="请输入订单号"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="商品名称">
          <el-input
            v-model="searchForm.itemName"
            placeholder="请输入商品名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.userName"
            placeholder="请输入用户名"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="待处理" value="PENDING" />
            <el-option label="进行中" value="ACTIVE" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <el-table
        :data="orders"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="订单号" width="100" />
        <el-table-column prop="itemName" label="商品名称" min-width="150" />
        <el-table-column prop="userName" label="用户" width="120" />
        <el-table-column prop="startDate" label="开始日期" width="120" />
        <el-table-column prop="endDate" label="结束日期" width="120" />
        <el-table-column prop="totalPrice" label="总价" width="100">
          <template #default="{ row }">
            ¥{{ row.totalPrice.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="150" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="primary"
              :icon="View"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              v-if="row.status === 'PENDING'"
              size="small"
              type="success"
              @click="handleApprove(row)"
            >
              批准
            </el-button>
            <el-button
              v-if="['PENDING', 'ACTIVE'].includes(row.status)"
              size="small"
              type="danger"
              @click="handleCancel(row)"
            >
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建订单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建订单"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="orderFormRef"
        :model="orderForm"
        :rules="orderFormRules"
        label-width="80px"
      >
        <el-form-item label="选择商品" prop="itemId">
          <el-select
            v-model="orderForm.itemId"
            placeholder="请选择商品"
            style="width: 100%"
            @change="handleItemChange"
          >
            <el-option
              v-for="item in availableItems"
              :key="item.id"
              :label="`${item.name} (¥${item.dailyPrice}/天)`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="租赁日期" prop="dateRange">
          <el-date-picker
            v-model="orderForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
            @change="calculateTotalPrice"
          />
        </el-form-item>
        
        <el-form-item label="总价">
          <el-input
            :value="`¥${orderForm.totalPrice.toFixed(2)}`"
            readonly
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleCreateOrder"
        >
          创建订单
        </el-button>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="订单详情"
      width="600px"
    >
      <div v-if="selectedOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ selectedOrder.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedOrder.status)">
              {{ getStatusText(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="商品名称">{{ selectedOrder.itemName }}</el-descriptions-item>
          <el-descriptions-item label="用户">{{ selectedOrder.userName }}</el-descriptions-item>
          <el-descriptions-item label="开始日期">{{ selectedOrder.startDate }}</el-descriptions-item>
          <el-descriptions-item label="结束日期">{{ selectedOrder.endDate }}</el-descriptions-item>
          <el-descriptions-item label="总价">¥{{ selectedOrder.totalPrice.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ selectedOrder.createdAt }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { Plus, View } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import type { Item } from '@/types'
import { useOrdersStore } from '@/stores/orders'

// Store
const ordersStore = useOrdersStore()

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const selectedOrders = ref([])
const selectedOrder = ref(null)

// 计算属性
const orders = computed(() => ordersStore.orders)
const total = computed(() => ordersStore.orders.length)

// 表单引用
const orderFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  orderId: '',
  itemName: '',
  userName: '',
  status: ''
})

// 订单表单
const orderForm = reactive({
  itemId: null,
  dateRange: [],
  totalPrice: 0
})

// 可用商品列表
const availableItems = ref<Item[]>([])

// 删除原来的订单列表，现在使用store中的数据

// 表单验证规则
const orderFormRules: FormRules = {
  itemId: [
    { required: true, message: '请选择商品', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '请选择租赁日期', trigger: 'change' }
  ]
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: '',
    ACTIVE: 'success',
    COMPLETED: 'info',
    CANCELLED: 'danger'
  }
  return statusMap[status] || ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: '待处理',
    ACTIVE: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadOrders()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    orderId: '',
    itemName: '',
    userName: '',
    status: ''
  })
  handleSearch()
}

// 选择改变
const handleSelectionChange = (selection: any[]) => {
  selectedOrders.value = selection
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadOrders()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadOrders()
}

// 查看订单详情
const handleView = (order: any) => {
  selectedOrder.value = order
  showDetailDialog.value = true
}

// 批准订单
const handleApprove = (order: any) => {
  ElMessageBox.confirm(
    `确定要批准订单 ${order.id} 吗？`,
    '确认批准',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ordersStore.approveOrder(order.id)
  })
}

// 取消订单
const handleCancel = (order: any) => {
  ElMessageBox.confirm(
    `确定要取消订单 ${order.id} 吗？`,
    '确认取消',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ordersStore.cancelOrder(order.id)
  })
}

// 商品选择改变
const handleItemChange = () => {
  calculateTotalPrice()
}

// 计算总价
const calculateTotalPrice = () => {
  if (orderForm.itemId && orderForm.dateRange && orderForm.dateRange.length === 2) {
    const item = availableItems.value.find(item => item.id === orderForm.itemId)
    if (item) {
      const startDate = dayjs(orderForm.dateRange[0])
      const endDate = dayjs(orderForm.dateRange[1])
      const days = endDate.diff(startDate, 'day') + 1
      orderForm.totalPrice = item.dailyPrice * days
    }
  } else {
    orderForm.totalPrice = 0
  }
}

// 对话框关闭
const handleDialogClose = () => {
  resetOrderForm()
}

// 重置订单表单
const resetOrderForm = () => {
  Object.assign(orderForm, {
    itemId: null,
    dateRange: [],
    totalPrice: 0
  })
  orderFormRef.value?.resetFields()
}

// 创建订单
const handleCreateOrder = async () => {
  if (!orderFormRef.value) return

  await orderFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const newOrder = {
          itemName: availableItems.value.find(item => item.id === orderForm.itemId)?.name || '',
          userName: '当前用户', // 实际应该从用户信息获取
          startDate: dayjs(orderForm.dateRange[0]).format('YYYY-MM-DD'),
          endDate: dayjs(orderForm.dateRange[1]).format('YYYY-MM-DD'),
          totalPrice: orderForm.totalPrice,
          status: 'PENDING' as const,
          createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
        }

        ordersStore.addOrder(newOrder)
        showCreateDialog.value = false
      } catch (error) {
        ElMessage.error('创建订单失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 加载订单列表
const loadOrders = () => {
  loading.value = true
  ordersStore.loadOrders()
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 加载可用商品
const loadAvailableItems = () => {
  // TODO: 调用API加载可用商品
  availableItems.value = [
    {
      id: 1,
      name: 'MacBook Pro 16"',
      dailyPrice: 89.99,
      status: 'AVAILABLE'
    },
    {
      id: 3,
      name: 'DJI Mavic 3 Pro',
      dailyPrice: 120.00,
      status: 'AVAILABLE'
    }
  ] as Item[]
}

onMounted(() => {
  loadOrders()
  loadAvailableItems()
})
</script>

<style lang="scss" scoped>
.orders-view {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-left {
      h1 {
        margin: 0 0 8px;
        color: var(--dark-color);
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #666;
      }
    }
  }

  .search-card,
  .table-card {
    margin-bottom: 24px;
    border: none;
    box-shadow: var(--box-shadow);
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .order-detail {
    .el-descriptions {
      :deep(.el-descriptions__label) {
        font-weight: 600;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .orders-view {
    .page-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
    }

    .search-card {
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 16px;

          .el-input,
          .el-select {
            width: 100% !important;
          }
        }
      }
    }

    .table-card {
      :deep(.el-table) {
        .el-table__body-wrapper {
          overflow-x: auto;
        }
      }
    }
  }
}
</style>
