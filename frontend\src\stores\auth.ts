import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm, RegisterForm } from '@/types'
import { authApi } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'ADMIN')

  // 初始化用户信息
  const initUser = async () => {
    if (token.value) {
      try {
        const userData = await authApi.getUserInfo()
        user.value = userData
        return true
      } catch (error) {
        // 如果获取用户信息失败，清除token并抛出错误
        token.value = null
        user.value = null
        localStorage.removeItem('token')
        localStorage.removeItem('rememberMe')
        throw error
      }
    }
    return false
  }

  // 登录
  const login = async (loginForm: LoginForm) => {
    loading.value = true
    try {
      const response = await authApi.login(loginForm)
      token.value = response.token
      user.value = response.user

      // 保存到localStorage
      localStorage.setItem('token', response.token)
      if (loginForm.rememberMe) {
        localStorage.setItem('rememberMe', 'true')
      }

      ElMessage.success('登录成功')
      return true
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerForm: RegisterForm) => {
    loading.value = true
    try {
      const response = await authApi.register(registerForm)
      token.value = response.token
      user.value = response.user

      // 保存到localStorage
      localStorage.setItem('token', response.token)

      ElMessage.success('注册成功')
      return true
    } catch (error: any) {
      ElMessage.error(error.message || '注册失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = (showMessage = true) => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('rememberMe')
    if (showMessage) {
      ElMessage.success('已退出登录')
    }
  }

  return {
    token,
    user,
    loading,
    isAuthenticated,
    isAdmin,
    initUser,
    login,
    register,
    logout
  }
})
