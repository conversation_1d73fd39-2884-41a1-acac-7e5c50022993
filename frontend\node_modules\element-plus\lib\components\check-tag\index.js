'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var checkTag$1 = require('./src/check-tag.js');
var checkTag = require('./src/check-tag2.js');
var install = require('../../utils/vue/install.js');

const ElCheckTag = install.withInstall(checkTag$1["default"]);

exports.checkTagEmits = checkTag.checkTagEmits;
exports.checkTagProps = checkTag.checkTagProps;
exports.ElCheckTag = ElCheckTag;
exports["default"] = ElCheckTag;
//# sourceMappingURL=index.js.map
