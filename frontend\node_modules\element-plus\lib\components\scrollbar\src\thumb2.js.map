{"version": 3, "file": "thumb2.js", "sources": ["../../../../../../packages/components/scrollbar/src/thumb.vue"], "sourcesContent": ["<template>\n  <transition :name=\"ns.b('fade')\">\n    <div\n      v-show=\"always || visible\"\n      ref=\"instance\"\n      :class=\"[ns.e('bar'), ns.is(bar.key)]\"\n      @mousedown=\"clickTrackHandler\"\n      @click.stop\n    >\n      <div\n        ref=\"thumb\"\n        :class=\"ns.e('thumb')\"\n        :style=\"thumbStyle\"\n        @mousedown=\"clickThumbHandler\"\n      />\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, ref, toRef } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { isClient, throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { scrollbarContextKey } from './constants'\nimport { BAR_MAP, renderThumbStyle } from './util'\nimport { thumbProps } from './thumb'\n\nconst COMPONENT_NAME = 'Thumb'\nconst props = defineProps(thumbProps)\n\nconst scrollbar = inject(scrollbarContextKey)\nconst ns = useNamespace('scrollbar')\n\nif (!scrollbar) throwError(COMPONENT_NAME, 'can not inject scrollbar context')\n\nconst instance = ref<HTMLDivElement>()\nconst thumb = ref<HTMLDivElement>()\n\nconst thumbState = ref<Partial<Record<'X' | 'Y', number>>>({})\nconst visible = ref(false)\n\nlet cursorDown = false\nlet cursorLeave = false\nlet baseScrollHeight = 0\nlet originalOnSelectStart:\n  | ((this: GlobalEventHandlers, ev: Event) => any)\n  | null = isClient ? document.onselectstart : null\n\nconst bar = computed(() => BAR_MAP[props.vertical ? 'vertical' : 'horizontal'])\n\nconst thumbStyle = computed(() =>\n  renderThumbStyle({\n    size: props.size,\n    move: props.move,\n    bar: bar.value,\n  })\n)\n\nconst offsetRatio = computed(\n  () =>\n    // offsetRatioX = original width of thumb / current width of thumb / ratioX\n    // offsetRatioY = original height of thumb / current height of thumb / ratioY\n    // instance height = wrap height - GAP\n    instance.value![bar.value.offset] ** 2 /\n    scrollbar.wrapElement![bar.value.scrollSize] /\n    props.ratio /\n    thumb.value![bar.value.offset]\n)\n\nconst clickThumbHandler = (e: MouseEvent) => {\n  // prevent click event of middle and right button\n  e.stopPropagation()\n  if (e.ctrlKey || [1, 2].includes(e.button)) return\n\n  window.getSelection()?.removeAllRanges()\n  startDrag(e)\n\n  const el = e.currentTarget as HTMLDivElement\n  if (!el) return\n  thumbState.value[bar.value.axis] =\n    el[bar.value.offset] -\n    (e[bar.value.client] - el.getBoundingClientRect()[bar.value.direction])\n}\n\nconst clickTrackHandler = (e: MouseEvent) => {\n  if (!thumb.value || !instance.value || !scrollbar.wrapElement) return\n\n  const offset = Math.abs(\n    (e.target as HTMLElement).getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]\n  )\n  const thumbHalf = thumb.value[bar.value.offset] / 2\n  const thumbPositionPercentage =\n    ((offset - thumbHalf) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n\n  scrollbar.wrapElement[bar.value.scroll] =\n    (thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize]) /\n    100\n}\n\nconst startDrag = (e: MouseEvent) => {\n  e.stopImmediatePropagation()\n  cursorDown = true\n  baseScrollHeight = scrollbar.wrapElement.scrollHeight\n  document.addEventListener('mousemove', mouseMoveDocumentHandler)\n  document.addEventListener('mouseup', mouseUpDocumentHandler)\n  originalOnSelectStart = document.onselectstart\n  document.onselectstart = () => false\n}\n\nconst mouseMoveDocumentHandler = (e: MouseEvent) => {\n  if (!instance.value || !thumb.value) return\n  if (cursorDown === false) return\n\n  const prevPage = thumbState.value[bar.value.axis]\n  if (!prevPage) return\n\n  const offset =\n    (instance.value.getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]) *\n    -1\n  const thumbClickPosition = thumb.value[bar.value.offset] - prevPage\n  const thumbPositionPercentage =\n    ((offset - thumbClickPosition) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n  scrollbar.wrapElement[bar.value.scroll] =\n    (thumbPositionPercentage * baseScrollHeight) / 100\n}\n\nconst mouseUpDocumentHandler = () => {\n  cursorDown = false\n  thumbState.value[bar.value.axis] = 0\n  document.removeEventListener('mousemove', mouseMoveDocumentHandler)\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n  restoreOnselectstart()\n  if (cursorLeave) visible.value = false\n}\n\nconst mouseMoveScrollbarHandler = () => {\n  cursorLeave = false\n  visible.value = !!props.size\n}\n\nconst mouseLeaveScrollbarHandler = () => {\n  cursorLeave = true\n  visible.value = cursorDown\n}\n\nonBeforeUnmount(() => {\n  restoreOnselectstart()\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n})\n\nconst restoreOnselectstart = () => {\n  if (document.onselectstart !== originalOnSelectStart)\n    document.onselectstart = originalOnSelectStart\n}\n\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mousemove',\n  mouseMoveScrollbarHandler\n)\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mouseleave',\n  mouseLeaveScrollbarHandler\n)\n</script>\n"], "names": ["inject", "scrollbarContextKey", "useNamespace", "throwError", "ref", "isClient", "computed", "BAR_MAP", "renderThumbStyle", "onBeforeUnmount", "useEventListener", "toRef", "_createBlock", "_Transition", "_unref", "_withCtx", "_withDirectives", "_createElementVNode", "_normalizeClass"], "mappings": ";;;;;;;;;;;;;;;;;;;AA+BA,IAAM,MAAA,SAAA,GAAYA,WAAOC,6BAAmB,CAAA,CAAA;AAC5C,IAAM,MAAA,EAAA,GAAKC,mBAAa,WAAW,CAAA,CAAA;AAEnC,IAAA,IAAI,CAAC,SAAA;AAEL,MAAAC,+BAAqC,EAAA,kCAAA,CAAA,CAAA;AACrC,IAAA,MAAM,QAAQ,GAAoBC,OAAA,EAAA,CAAA;AAElC,IAAM,MAAA,KAAA,GAAAA,OAAA,EAAa,CAAwC;AAC3D,IAAM,MAAA,UAAU,UAAS,CAAA,EAAA,CAAA,CAAA;AAEzB,IAAA,MAAiB,OAAA,GAAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACjB,IAAA,IAAI,UAAc,GAAA,KAAA,CAAA;AAClB,IAAA,IAAI,WAAmB,GAAA,KAAA,CAAA;AACvB,IAAI,IAAA,gBAAA,GAAA,CAAA,CAAA;AAIJ,IAAM,IAAA,qBAAqB,GAAAC,wBAAyB,CAAA,aAAA,GAAA;AAEpD,IAAA,MAAM,GAAa,GAAAC,YAAA,CAAA,MAAAC,YAAA,CAAA,KAAA,CAAA,QAAA,GAAA,UAAA,GAAA,YAAA,CAAA,CAAA,CAAA;AAAA,IAAA,gBACA,GAAAD,YAAA,CAAA,MAAAE,qBAAA,CAAA;AAAA,MAAA,WACH,CAAA,IAAA;AAAA,MAAA,WACA,CAAA,IAAA;AAAA,MAAA,QACH,CAAA,KAAA;AAAA,KAAA,CACX,CAAC,CAAA;AAAA,IACH,MAAA,WAAA,GAAAF,YAAA,CAAA,MAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,WAAA,CAAA,GAAA,CAAA,KAAA,CAAA,UAAA,CAAA,GAAA,KAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAEA,IAAA,MAAM,iBAAc,GAAA,CAAA,CAAA,KAAA;AAAA,MAClB,IAAA,EAAA,CAAA;AAAA,MAAA,CAAA,CAAA,eAAA,EAAA,CAAA;AAAA,MAAA,IAAA,CAAA,CAAA,OAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,QAAA,OAAA;AAAA,MAAA,CAAA,WAIkB,CAAA,cAAU,KAAM,IAAA,QACtB,CAAA,GAAA,EAAA,CAAA,iBAAiB,CAAM;AAEJ,MAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MACjC,MAAA,EAAA,GAAA,CAAA,CAAA,aAAA,CAAA;AAEA,MAAM,IAAA,CAAA,EAAA;AAEJ,QAAE,OAAgB;AAClB,MAAI,iBAAa,GAAC,CAAG,KAAG,CAAA,IAAA,CAAA,GAAW,EAAA,CAAA,GAAM,CAAG,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,EAAA,CAAA,qBAAA,EAAA,CAAA,GAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AAE5C,KAAO,CAAA;AACP,IAAA,MAAA,iBAAW,GAAA,CAAA,CAAA,KAAA;AAEX,MAAA,IAAA,CAAA,MAAW,KAAE,IAAA,CAAA,QAAA,CAAA,KAAA,IAAA,CAAA,SAAA,CAAA,WAAA;AACb,QAAA,OAAS;AACT,MAAW,MAAA,MAAA,GAAA,QAAgB,CAAA,CAAA,CAAA,4BACN,EAAA,CAAA,IAChB,KAAI,CAAA,UAAY,GAAI,CAAA,CAAA;AAA8C,MACzE,MAAA,SAAA,GAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA;AAEA,MAAM,MAAA,uBAAuC,GAAA,CAAA,MAAA,GAAA,SAAA,IAAA,GAAA,GAAA,WAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AAC3C,MAAI,qBAAiB,UAAkB,CAAA,MAAA,CAAA,0BAAwB,GAAA,SAAA,CAAA,WAAA,CAAA,GAAA,CAAA,KAAA,CAAA,UAAA,CAAA,GAAA,GAAA,CAAA;AAE/D,KAAA,CAAA;AAAoB,IACjB,MAAE,SAAuB,GAAA,CAAA,CAAA,KAAA;AACN,MACtB,CAAA,CAAA,wBAAA,EAAA,CAAA;AACA,MAAA,kBAAkB;AAClB,MAAM,gBAAA,GAAA,SAAA,CAAA,WACO,CAAA,YAAA,CAAA;AAGb,MAAU,QAAA,CAAA,gBAAgB,CAAA,WAAY,EAAA,wBACT,CAAA,CAAA;AAC3B,MACJ,QAAA,CAAA,gBAAA,CAAA,SAAA,EAAA,sBAAA,CAAA,CAAA;AAEA,MAAM,qBAA+B,GAAA,QAAA,CAAA,aAAA,CAAA;AACnC,MAAA,QAA2B,CAAA,aAAA,GAAA,MAAA,KAAA,CAAA;AAC3B,KAAa,CAAA;AACb,IAAA,MAAA,2BAAyC,CAAA,CAAA,KAAA;AACzC,MAAS,IAAA,CAAA,QAAA,CAAA,KAAA,IAAA,CAAA;AACT,QAAS,OAAA;AACT,MAAA,IAAA,UAAA,KAAA,KAAwB;AACxB,QAAA,OAAS;AAAsB,MACjC,MAAA,QAAA,GAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAEA,MAAM,IAAA,CAAA,QAAA;AACJ,QAAA,OAAK;AACL,MAAA,wBAA0B,CAAA,KAAA,CAAA,qBAAA,EAAA,CAAA,GAAA,CAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAE1B,MAAA,MAAM,kBAAW,GAAA,KAAiB,CAAA,KAAI,UAAU,CAAA,MAAA,CAAA,GAAA,QAAA,CAAA;AAChD,MAAA,MAAe,uBAAA,GAAA,CAAA,MAAA,GAAA,kBAAA,IAAA,GAAA,GAAA,WAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AAEf,MAAA,SACG,CAAA,WAAA,CAAA,GAAS,CAAM,KAAA,CAAA,MAAA,CAAA,GAAA,uBAAkC,GAAA,gBAC1C,GAAA,GAAM;AAEhB,KAAA,CAAA;AACA,IAAM,MAAA,sBAAA,GAAA,MACO;AAEb,MAAA,UAAU;AACuC,MACnD,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AAEA,MAAA,6BAA+B,WAAM,EAAA,wBAAA,CAAA,CAAA;AACnC,MAAa,QAAA,CAAA,mBAAA,CAAA,SAAA,EAAA,sBAAA,CAAA,CAAA;AACb,MAAA,oBAAiB,EAAU,CAAA;AAC3B,MAAS,IAAA,WAAA;AACT,QAAS,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACT,KAAqB,CAAA;AACrB,IAAI,MAAA,yBAA6B,GAAA,MAAA;AAAA,MACnC,WAAA,GAAA,KAAA,CAAA;AAEA,MAAA;AACE,KAAc,CAAA;AACd,IAAQ,MAAA,0BAAgB,GAAA,MAAA;AAAA,MAC1B,WAAA,GAAA,IAAA,CAAA;AAEA,MAAA;AACE,KAAc,CAAA;AACd,IAAAG,mBAAgB,CAAA,MAAA;AAAA,MAClB,oBAAA,EAAA,CAAA;AAEA,MAAA,QAAA,CAAA,mBAAsB,CAAA,SAAA,EAAA,sBAAA,CAAA,CAAA;AACpB,KAAqB,CAAA,CAAA;AACrB,IAAS,MAAA,oBAAA,GAAA;AAAqD,MAC/D,IAAA,QAAA,CAAA,aAAA,KAAA,qBAAA;AAED,QAAA,yBAA6B,qBAAM,CAAA;AACjC,KAAA,CAAA;AACE,IAAAC,qBAAyB,CAAAC,SAAA,CAAA,SAAA,EAAA,kBAAA,CAAA,EAAA,WAAA,EAAA,yBAAA,CAAA,CAAA;AAAA,IAC7BD,qBAAA,CAAAC,SAAA,CAAA,SAAA,EAAA,kBAAA,CAAA,EAAA,YAAA,EAAA,0BAAA,CAAA,CAAA;AAEA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACE,oBAAiB,EAAkB,EAAAC,eAAA,CAAAC,cAAA,EAAA;AAAA,QACnC,IAAA,EAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,QACA,SAAA,EAAA,EAAA;AAAA,OACF,EAAA;AACA,QAAA,OAAA,EAAAC,WAAA,CAAA,MAAA;AAAA,UACEC,kBAAmC,CAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,YACnC,OAAA,EAAA,UAAA;AAAA,YACA,GAAA,EAAA,QAAA;AAAA,YACF,KAAA,EAAAC,kBAAA,CAAA,CAAAJ,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,EAAAA,SAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;"}