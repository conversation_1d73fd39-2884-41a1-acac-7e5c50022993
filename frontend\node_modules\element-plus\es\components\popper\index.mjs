import Popper from './src/popper2.mjs';
export { default as <PERSON>PopperArrow } from './src/arrow2.mjs';
export { default as ElPopperTrigger } from './src/trigger.mjs';
export { default as ElPopperContent } from './src/content2.mjs';
export { Effect, popperProps, roleTypes, usePopperProps } from './src/popper.mjs';
export { popperTriggerProps, usePopperTriggerProps } from './src/trigger2.mjs';
export { popperContentEmits, popperContentProps, popperCoreConfigProps, usePopperContentEmits, usePopperContentProps, usePopperCoreConfigProps } from './src/content.mjs';
export { popperArrowProps, usePopperArrowProps } from './src/arrow.mjs';
export { POPPER_CONTENT_INJECTION_KEY, POPPER_INJECTION_KEY } from './src/constants.mjs';
import { withInstall } from '../../utils/vue/install.mjs';

const ElPopper = withInstall(Popper);

export { ElPopper, ElPopper as default };
//# sourceMappingURL=index.mjs.map
