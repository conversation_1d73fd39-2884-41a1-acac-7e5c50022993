{"version": 3, "file": "transfer.js", "sources": ["../../../../../../packages/components/transfer/src/transfer.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <transfer-panel\n      ref=\"leftPanel\"\n      :data=\"sourceData\"\n      :option-render=\"optionRender\"\n      :placeholder=\"panelFilterPlaceholder\"\n      :title=\"leftPanelTitle\"\n      :filterable=\"filterable\"\n      :format=\"format\"\n      :filter-method=\"filterMethod\"\n      :default-checked=\"leftDefaultChecked\"\n      :props=\"props.props\"\n      @checked-change=\"onSourceCheckedChange\"\n    >\n      <template #empty>\n        <slot name=\"left-empty\" />\n      </template>\n      <slot name=\"left-footer\" />\n    </transfer-panel>\n    <div :class=\"ns.e('buttons')\">\n      <el-button\n        type=\"primary\"\n        :class=\"[ns.e('button'), ns.is('with-texts', hasButtonTexts)]\"\n        :disabled=\"isEmpty(checkedState.rightChecked)\"\n        @click=\"addToLeft\"\n      >\n        <el-icon><arrow-left /></el-icon>\n        <span v-if=\"!isUndefined(buttonTexts[0])\">{{ buttonTexts[0] }}</span>\n      </el-button>\n      <el-button\n        type=\"primary\"\n        :class=\"[ns.e('button'), ns.is('with-texts', hasButtonTexts)]\"\n        :disabled=\"isEmpty(checkedState.leftChecked)\"\n        @click=\"addToRight\"\n      >\n        <span v-if=\"!isUndefined(buttonTexts[1])\">{{ buttonTexts[1] }}</span>\n        <el-icon><arrow-right /></el-icon>\n      </el-button>\n    </div>\n    <transfer-panel\n      ref=\"rightPanel\"\n      :data=\"targetData\"\n      :option-render=\"optionRender\"\n      :placeholder=\"panelFilterPlaceholder\"\n      :filterable=\"filterable\"\n      :format=\"format\"\n      :filter-method=\"filterMethod\"\n      :title=\"rightPanelTitle\"\n      :default-checked=\"rightDefaultChecked\"\n      :props=\"props.props\"\n      @checked-change=\"onTargetCheckedChange\"\n    >\n      <template #empty>\n        <slot name=\"right-empty\" />\n      </template>\n      <slot name=\"right-footer\" />\n    </transfer-panel>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { Comment, computed, h, reactive, ref, useSlots, watch } from 'vue'\nimport { debugWarn, isEmpty, isUndefined } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { ElButton } from '@element-plus/components/button'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useFormItem } from '@element-plus/components/form'\nimport { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'\nimport { transferEmits, transferProps } from './transfer'\nimport {\n  useCheckedChange,\n  useComputedData,\n  useMove,\n  usePropsAlias,\n} from './composables'\nimport TransferPanel from './transfer-panel.vue'\n\nimport type {\n  TransferCheckedState,\n  TransferDataItem,\n  TransferDirection,\n} from './transfer'\nimport type { TransferPanelInstance } from './transfer-panel'\n\ndefineOptions({\n  name: 'ElTransfer',\n})\n\nconst props = defineProps(transferProps)\nconst emit = defineEmits(transferEmits)\nconst slots = useSlots()\n\nconst { t } = useLocale()\nconst ns = useNamespace('transfer')\nconst { formItem } = useFormItem()\n\nconst checkedState = reactive<TransferCheckedState>({\n  leftChecked: [],\n  rightChecked: [],\n})\n\nconst propsAlias = usePropsAlias(props)\n\nconst { sourceData, targetData } = useComputedData(props)\n\nconst { onSourceCheckedChange, onTargetCheckedChange } = useCheckedChange(\n  checkedState,\n  emit\n)\n\nconst { addToLeft, addToRight } = useMove(props, checkedState, emit)\n\nconst leftPanel = ref<TransferPanelInstance>()\nconst rightPanel = ref<TransferPanelInstance>()\n\nconst clearQuery = (which: TransferDirection) => {\n  switch (which) {\n    case 'left':\n      leftPanel.value!.query = ''\n      break\n    case 'right':\n      rightPanel.value!.query = ''\n      break\n  }\n}\n\nconst hasButtonTexts = computed(() => props.buttonTexts.length === 2)\n\nconst leftPanelTitle = computed(\n  () => props.titles[0] || t('el.transfer.titles.0')\n)\n\nconst rightPanelTitle = computed(\n  () => props.titles[1] || t('el.transfer.titles.1')\n)\n\nconst panelFilterPlaceholder = computed(\n  () => props.filterPlaceholder || t('el.transfer.filterPlaceholder')\n)\n\nwatch(\n  () => props.modelValue,\n  () => {\n    if (props.validateEvent) {\n      formItem?.validate?.('change').catch((err) => debugWarn(err))\n    }\n  }\n)\n\nconst optionRender = computed(() => (option: TransferDataItem) => {\n  if (props.renderContent) return props.renderContent(h, option)\n\n  const defaultSlotVNodes = (slots.default?.({ option }) || []).filter(\n    (node) => node.type !== Comment\n  )\n  if (defaultSlotVNodes.length) {\n    return defaultSlotVNodes\n  }\n\n  return h(\n    'span',\n    option[propsAlias.value.label] || option[propsAlias.value.key]\n  )\n})\n\ndefineExpose({\n  /** @description clear the filter keyword of a certain panel */\n  clearQuery,\n  /** @description left panel ref */\n  leftPanel,\n  /** @description right panel ref */\n  rightPanel,\n})\n</script>\n"], "names": ["useSlots", "useLocale", "useNamespace", "useFormItem", "reactive", "usePropsAlias", "useComputedData", "useCheckedChange", "useMove", "ref", "computed", "watch", "debugWarn", "h", "Comment", "_normalizeClass", "_unref", "_createVNode", "TransferPanel"], "mappings": ";;;;;;;;;;;;;;;;;;;;;uCAqFc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,QAAQA,YAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIC,eAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAKC,qBAAa,UAAU,CAAA,CAAA;AAClC,IAAM,MAAA,EAAE,QAAS,EAAA,GAAIC,uBAAY,EAAA,CAAA;AAEjC,IAAA,MAAM,eAAeC,YAA+B,CAAA;AAAA,MAClD,aAAa,EAAC;AAAA,MACd,cAAc,EAAC;AAAA,KAChB,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAaC,4BAAc,KAAK,CAAA,CAAA;AAEtC,IAAA,MAAM,EAAE,UAAA,EAAY,UAAW,EAAA,GAAIC,gCAAgB,KAAK,CAAA,CAAA;AAExD,IAAM,MAAA,EAAE,qBAAuB,EAAA,qBAAA,EAA0B,GAAAC,iCAAA,CAAA,YAAA,EAAA,IAAA,CAAA,CAAA;AAAA,IACvD,MAAA,EAAA,SAAA,EAAA,UAAA,EAAA,GAAAC,eAAA,CAAA,KAAA,EAAA,YAAA,EAAA,IAAA,CAAA,CAAA;AAAA,IACA,MAAA,SAAA,GAAAC,OAAA,EAAA,CAAA;AAAA,IACF,MAAA,UAAA,GAAAA,OAAA,EAAA,CAAA;AAEA,IAAA,MAAM,UAAa,GAAA,CAAA,KAAA,KAAW;AAE9B,MAAA;AACA,QAAA;AAEA,UAAM,SAAA,CAAA,KAA2C,CAAA,KAAA,GAAA,EAAA,CAAA;AAC/C,UAAA,MAAe;AAAA,QACb,KAAK,OAAA;AACH,UAAA,UAAU,MAAO,CAAQ,KAAA,GAAA,EAAA,CAAA;AACzB,UAAA,MAAA;AAAA,OAAA;AAEA,KAAA,CAAA;AACA,IAAA,MAAA,cAAA,GAAAC,YAAA,CAAA,MAAA,KAAA,CAAA,WAAA,CAAA,MAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACJ,MAAA,cAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,sBAAA,CAAA,CAAA,CAAA;AAAA,IACF,MAAA,eAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,sBAAA,CAAA,CAAA,CAAA;AAEA,IAAA,MAAM,sBAA0B,GAAAA,YAAY,CAAA,MAAA,KAAA,CAAA,iBAAwB,IAAA,CAAA,CAAA,+BAAA,CAAA,CAAA,CAAA;AAEpE,IAAAC,SAAA,CAAM,MAAiB,KAAA,CAAA,UAAA,EAAA,MAAA;AAAA,MACrB,MAAM,CAAM;AAAqC,MACnD,IAAA,KAAA,CAAA,aAAA,EAAA;AAEA,QAAA,CAAA,EAAwB,GAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAC,eAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA;AAC2B,KACnD,CAAA,CAAA;AAEA,IAAA,MAAM,YAAyB,GAAAF,YAAA,CAAA,MAAA,CAAA,MAAA,KAAA;AAAA,MAC7B,IAAM,EAAA,CAAA;AAA4D,MACpE,IAAA,KAAA,CAAA,aAAA;AAEA,QAAA,OAAA,KAAA,CAAA,aAAA,CAAAG,KAAA,EAAA,MAAA,CAAA,CAAA;AAAA,MACE,MAAM,iBAAM,GAAA,CAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,EAAA,MAAA,EAAA,CAAA,KAAA,EAAA,EAAA,MAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,IAAA,KAAAC,WAAA,CAAA,CAAA;AAAA,MACZ,IAAM,iBAAA,CAAA,MAAA,EAAA;AACJ,QAAA,wBAAyB,CAAA;AACvB,OAAU;AAAkD,MAC9D,OAAAD,KAAA,CAAA,MAAA,EAAA,MAAA,CAAA,UAAA,CAAA,KAAA,CAAA,KAAA,CAAA,IAAA,MAAA,CAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAAA,IACF,MAAA,CAAA;AAEA,MAAA,UAAqB;AACnB,MAAA;AAEA,MAAM,UAAA;AAAwD,KAC5D,CAAA,CAAA;AAAwB,IAC1B,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AACA,MAAA,8CAA8B,CAAA,KAAA,EAAA;AAC5B,QAAO,KAAA,EAAAE,kBAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AAAA,OACT,EAAA;AAEA,QAAOC,eAAA,CAAAC,wBAAA,EAAA;AAAA,UACL,OAAA,EAAA,WAAA;AAAA,UACA,GAAA,EAAO;AAAsD,UAC/D,IAAA,EAAAF,SAAA,CAAA,UAAA,CAAA;AAAA,UACD,eAAA,EAAAA,SAAA,CAAA,YAAA,CAAA;AAED,UAAa,WAAA,EAAAA,SAAA,CAAA,sBAAA,CAAA;AAAA,UAAA,KAAA,EAAAA,SAAA,CAAA,cAAA,CAAA;AAAA,UAEX,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,UAAA,MAAA,EAAA,IAAA,CAAA,MAAA;AAAA,UAEA,eAAA,EAAA,IAAA,CAAA,YAAA;AAAA,UAAA,iBAAA,EAAA,IAAA,CAAA,kBAAA;AAAA,UAEA,KAAA,EAAA,KAAA,CAAA,KAAA;AAAA,UACD,eAAA,EAAAA,SAAA,CAAA,qBAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}