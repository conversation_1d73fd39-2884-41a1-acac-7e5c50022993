import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { RentalOrder } from '@/types'
import { ElMessage } from 'element-plus'

export const useOrdersStore = defineStore('orders', () => {
  // 状态
  const orders = ref<RentalOrder[]>([
    {
      id: 1001,
      itemName: 'MacBook Pro 16"',
      userName: '张三',
      startDate: '2024-01-15',
      endDate: '2024-01-20',
      totalPrice: 449.95,
      status: 'ACTIVE',
      createdAt: '2024-01-14 10:30:00'
    },
    {
      id: 1002,
      itemName: 'Canon EOS R5',
      userName: '李四',
      startDate: '2024-01-14',
      endDate: '2024-01-18',
      totalPrice: 278.00,
      status: 'COMPLETED',
      createdAt: '2024-01-13 15:20:00'
    },
    {
      id: 1003,
      itemName: 'DJI Mavic 3 Pro',
      userName: '王五',
      startDate: '2024-01-16',
      endDate: '2024-01-21',
      totalPrice: 600.00,
      status: 'PENDING',
      createdAt: '2024-01-15 09:15:00'
    }
  ])

  const loading = ref(false)

  // 更新订单状态
  const updateOrderStatus = (orderId: number, status: string) => {
    const order = orders.value.find(o => o.id === orderId)
    if (order) {
      order.status = status
      // 保存到localStorage以持久化状态
      localStorage.setItem('orders', JSON.stringify(orders.value))
      return true
    }
    return false
  }

  // 批准订单
  const approveOrder = (orderId: number) => {
    if (updateOrderStatus(orderId, 'ACTIVE')) {
      ElMessage.success('订单已批准')
      return true
    }
    ElMessage.error('订单批准失败')
    return false
  }

  // 取消订单
  const cancelOrder = (orderId: number) => {
    if (updateOrderStatus(orderId, 'CANCELLED')) {
      ElMessage.success('订单已取消')
      return true
    }
    ElMessage.error('订单取消失败')
    return false
  }

  // 完成订单
  const completeOrder = (orderId: number) => {
    if (updateOrderStatus(orderId, 'COMPLETED')) {
      ElMessage.success('订单已完成')
      return true
    }
    ElMessage.error('订单完成失败')
    return false
  }

  // 添加新订单
  const addOrder = (order: Omit<RentalOrder, 'id'>) => {
    const newOrder: RentalOrder = {
      ...order,
      id: Date.now()
    }
    orders.value.unshift(newOrder)
    localStorage.setItem('orders', JSON.stringify(orders.value))
    ElMessage.success('订单创建成功')
    return newOrder
  }

  // 从localStorage加载订单
  const loadOrders = () => {
    try {
      const savedOrders = localStorage.getItem('orders')
      if (savedOrders) {
        orders.value = JSON.parse(savedOrders)
      }
    } catch (error) {
      console.error('加载订单数据失败:', error)
    }
  }

  // 获取订单统计
  const getOrderStats = () => {
    return {
      total: orders.value.length,
      pending: orders.value.filter(o => o.status === 'PENDING').length,
      active: orders.value.filter(o => o.status === 'ACTIVE').length,
      completed: orders.value.filter(o => o.status === 'COMPLETED').length,
      cancelled: orders.value.filter(o => o.status === 'CANCELLED').length
    }
  }

  return {
    orders,
    loading,
    updateOrderStatus,
    approveOrder,
    cancelOrder,
    completeOrder,
    addOrder,
    loadOrders,
    getOrderStats
  }
})
