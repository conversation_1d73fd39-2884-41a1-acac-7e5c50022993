{"version": 3, "file": "space.js", "sources": ["../../../../../../packages/components/space/src/space.ts"], "sourcesContent": ["import {\n  Comment,\n  createTextVNode,\n  createVNode,\n  defineComponent,\n  isVNode,\n  renderSlot,\n} from 'vue'\nimport {\n  PatchFlags,\n  buildProps,\n  definePropType,\n  isArray,\n  isFragment,\n  isNumber,\n  isString,\n  isValidElementNode,\n} from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\nimport Item from './item'\nimport { useSpace } from './use-space'\n\nimport type {\n  ExtractPropTypes,\n  StyleValue,\n  VNode,\n  VNodeArrayChildren,\n  VNodeChild,\n} from 'vue'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { AlignItemsProperty } from 'csstype'\n\nexport const spaceProps = buildProps({\n  /**\n   * @description Placement direction\n   */\n  direction: {\n    type: String,\n    values: ['horizontal', 'vertical'],\n    default: 'horizontal',\n  },\n  /**\n   * @description Classname\n   */\n  class: {\n    type: definePropType<Arrayable<Record<string, boolean> | string>>([\n      String,\n      Object,\n      Array,\n    ]),\n    default: '',\n  },\n  /**\n   * @description Extra style rules\n   */\n  style: {\n    type: definePropType<StyleValue>([String, Array, Object]),\n    default: '',\n  },\n  /**\n   * @description Controls the alignment of items\n   */\n  alignment: {\n    type: definePropType<AlignItemsProperty>(String),\n    default: 'center',\n  },\n  /**\n   * @description Prefix for space-items\n   */\n  prefixCls: {\n    type: String,\n  },\n  /**\n   * @description Spacer\n   */\n  spacer: {\n    type: definePropType<VNodeChild>([Object, String, Number, Array]),\n    default: null,\n    validator: (val: unknown) => isVNode(val) || isNumber(val) || isString(val),\n  },\n  /**\n   * @description Auto wrapping\n   */\n  wrap: Boolean,\n  /**\n   * @description Whether to fill the container\n   */\n  fill: Boolean,\n  /**\n   * @description Ratio of fill\n   */\n  fillRatio: {\n    type: Number,\n    default: 100,\n  },\n  /**\n   * @description Spacing size\n   */\n  size: {\n    type: [String, Array, Number],\n    values: componentSizes,\n    validator: (val: unknown): val is [number, number] | number => {\n      return (\n        isNumber(val) ||\n        (isArray(val) && val.length === 2 && val.every(isNumber))\n      )\n    },\n  },\n} as const)\nexport type SpaceProps = ExtractPropTypes<typeof spaceProps>\n\nconst Space = defineComponent({\n  name: 'ElSpace',\n\n  props: spaceProps,\n\n  setup(props, { slots }) {\n    const { classes, containerStyle, itemStyle } = useSpace(props)\n\n    // retrieve the children out via a simple for loop\n    // the edge case here is that when users uses directives like <v-for>, <v-if>\n    // we need to go deeper until the child is not the Fragment type\n    function extractChildren(\n      children: VNodeArrayChildren,\n      parentKey = '',\n      extractedChildren: VNode[] = []\n    ) {\n      const { prefixCls } = props\n      children.forEach((child, loopKey) => {\n        if (isFragment(child)) {\n          if (isArray(child.children)) {\n            child.children.forEach((nested, key) => {\n              if (isFragment(nested) && isArray(nested.children)) {\n                extractChildren(\n                  nested.children,\n                  `${parentKey + key}-`,\n                  extractedChildren\n                )\n              } else {\n                if (isVNode(nested) && nested?.type === Comment) {\n                  extractedChildren.push(nested)\n                } else {\n                  extractedChildren.push(\n                    createVNode(\n                      Item,\n                      {\n                        style: itemStyle.value,\n                        prefixCls,\n                        key: `nested-${parentKey + key}`,\n                      },\n                      {\n                        default: () => [nested],\n                      },\n                      PatchFlags.PROPS | PatchFlags.STYLE,\n                      ['style', 'prefixCls']\n                    )\n                  )\n                }\n              }\n            })\n          }\n          // if the current child is valid vnode, then append this current vnode\n          // to item as child node.\n        } else if (isValidElementNode(child)) {\n          extractedChildren.push(\n            createVNode(\n              Item,\n              {\n                style: itemStyle.value,\n                prefixCls,\n                key: `LoopKey${parentKey + loopKey}`,\n              },\n              {\n                default: () => [child],\n              },\n              PatchFlags.PROPS | PatchFlags.STYLE,\n              ['style', 'prefixCls']\n            )\n          )\n        } else if (isVNode(child) && child.type === Comment) {\n          extractedChildren.push(child)\n        }\n      })\n\n      return extractedChildren\n    }\n\n    return () => {\n      const { spacer, direction } = props\n\n      const children = renderSlot(slots, 'default', { key: 0 }, () => [])\n\n      if ((children.children ?? []).length === 0) return null\n      // loop the children, if current children is rendered via `renderList` or `<v-for>`\n      if (isArray(children.children)) {\n        let extractedChildren = extractChildren(children.children)\n\n        if (spacer) {\n          // track the current rendering index, when encounters the last element\n          // then no need to add a spacer after it.\n          const len = extractedChildren.length - 1\n          extractedChildren = extractedChildren.reduce<VNode[]>(\n            (acc, child, idx) => {\n              const children = [...acc, child]\n              if (idx !== len) {\n                children.push(\n                  createVNode(\n                    'span',\n                    // adding width 100% for vertical alignment,\n                    // when the spacer inherit the width from the\n                    // parent, this span's width was not set, so space\n                    // might disappear\n                    {\n                      style: [\n                        itemStyle.value,\n                        direction === 'vertical' ? 'width: 100%' : null,\n                      ],\n                      key: idx,\n                    },\n                    [\n                      // if spacer is already a valid vnode, then append it to the current\n                      // span element.\n                      // otherwise, treat it as string.\n                      isVNode(spacer)\n                        ? spacer\n                        : createTextVNode(spacer as string, PatchFlags.TEXT),\n                    ],\n                    PatchFlags.STYLE\n                  )\n                )\n              }\n              return children\n            },\n            []\n          )\n        }\n\n        // spacer container.\n        return createVNode(\n          'div',\n          {\n            class: classes.value,\n            style: containerStyle.value,\n          },\n          extractedChildren,\n          PatchFlags.STYLE | PatchFlags.CLASS\n        )\n      }\n\n      return children.children\n    }\n  },\n})\n\nexport type SpaceInstance = InstanceType<typeof Space> & unknown\n\nexport default Space\n"], "names": ["buildProps", "definePropType", "isVNode", "isNumber", "isString", "componentSizes", "isArray", "defineComponent", "useSpace", "isFragment", "Comment", "createVNode", "<PERSON><PERSON>", "PatchFlags", "isValidElementNode", "renderSlot", "createTextVNode"], "mappings": ";;;;;;;;;;;;;AAqBY,MAAC,UAAU,GAAGA,kBAAU,CAAC;AACrC,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;AACtC,IAAI,OAAO,EAAE,YAAY;AACzB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEC,sBAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,KAAK,CAAC;AACN,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACzD,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,SAAS,EAAE,CAAC,GAAG,KAAKC,WAAO,CAAC,GAAG,CAAC,IAAIC,cAAQ,CAAC,GAAG,CAAC,IAAIC,eAAQ,CAAC,GAAG,CAAC;AACtE,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;AACjC,IAAI,MAAM,EAAEC,mBAAc;AAC1B,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK;AACxB,MAAM,OAAOF,cAAQ,CAAC,GAAG,CAAC,IAAIG,cAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,KAAK,CAACH,cAAQ,CAAC,CAAC;AACtF,KAAK;AACL,GAAG;AACH,CAAC,EAAE;AACE,MAAC,KAAK,GAAGI,mBAAe,CAAC;AAC9B,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE;AAC1B,IAAI,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,GAAGC,iBAAQ,CAAC,KAAK,CAAC,CAAC;AACnE,IAAI,SAAS,eAAe,CAAC,QAAQ,EAAE,SAAS,GAAG,EAAE,EAAE,iBAAiB,GAAG,EAAE,EAAE;AAC/E,MAAM,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;AAClC,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK;AAC3C,QAAQ,IAAIC,gBAAU,CAAC,KAAK,CAAC,EAAE;AAC/B,UAAU,IAAIH,cAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AACvC,YAAY,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK;AACpD,cAAc,IAAIG,gBAAU,CAAC,MAAM,CAAC,IAAIH,cAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;AAClE,gBAAgB,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;AAC3F,eAAe,MAAM;AACrB,gBAAgB,IAAIJ,WAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,MAAMQ,WAAO,EAAE;AAC5F,kBAAkB,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjD,iBAAiB,MAAM;AACvB,kBAAkB,iBAAiB,CAAC,IAAI,CAACC,eAAW,CAACC,eAAI,EAAE;AAC3D,oBAAoB,KAAK,EAAE,SAAS,CAAC,KAAK;AAC1C,oBAAoB,SAAS;AAC7B,oBAAoB,GAAG,EAAE,CAAC,OAAO,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC;AACpD,mBAAmB,EAAE;AACrB,oBAAoB,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC;AAC3C,mBAAmB,EAAEC,gBAAU,CAAC,KAAK,GAAGA,gBAAU,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;AACnF,iBAAiB;AACjB,eAAe;AACf,aAAa,CAAC,CAAC;AACf,WAAW;AACX,SAAS,MAAM,IAAIC,wBAAkB,CAAC,KAAK,CAAC,EAAE;AAC9C,UAAU,iBAAiB,CAAC,IAAI,CAACH,eAAW,CAACC,eAAI,EAAE;AACnD,YAAY,KAAK,EAAE,SAAS,CAAC,KAAK;AAClC,YAAY,SAAS;AACrB,YAAY,GAAG,EAAE,CAAC,OAAO,EAAE,SAAS,GAAG,OAAO,CAAC,CAAC;AAChD,WAAW,EAAE;AACb,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC;AAClC,WAAW,EAAEC,gBAAU,CAAC,KAAK,GAAGA,gBAAU,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;AAC3E,SAAS,MAAM,IAAIX,WAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAKQ,WAAO,EAAE;AAC7D,UAAU,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAM,OAAO,iBAAiB,CAAC;AAC/B,KAAK;AACL,IAAI,OAAO,MAAM;AACjB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;AAC1C,MAAM,MAAM,QAAQ,GAAGK,cAAU,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;AAC1E,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,MAAM,KAAK,CAAC;AACnE,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,IAAIT,cAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACtC,QAAQ,IAAI,iBAAiB,GAAG,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACnE,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,MAAM,GAAG,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;AACnD,UAAU,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,KAAK;AAC5E,YAAY,MAAM,SAAS,GAAG,CAAC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;AAC9C,YAAY,IAAI,GAAG,KAAK,GAAG,EAAE;AAC7B,cAAc,SAAS,CAAC,IAAI,CAACK,eAAW,CAAC,MAAM,EAAE;AACjD,gBAAgB,KAAK,EAAE;AACvB,kBAAkB,SAAS,CAAC,KAAK;AACjC,kBAAkB,SAAS,KAAK,UAAU,GAAG,aAAa,GAAG,IAAI;AACjE,iBAAiB;AACjB,gBAAgB,GAAG,EAAE,GAAG;AACxB,eAAe,EAAE;AACjB,gBAAgBT,WAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAGc,mBAAe,CAAC,MAAM,EAAEH,gBAAU,CAAC,IAAI,CAAC;AACnF,eAAe,EAAEA,gBAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AACpC,aAAa;AACb,YAAY,OAAO,SAAS,CAAC;AAC7B,WAAW,EAAE,EAAE,CAAC,CAAC;AACjB,SAAS;AACT,QAAQ,OAAOF,eAAW,CAAC,KAAK,EAAE;AAClC,UAAU,KAAK,EAAE,OAAO,CAAC,KAAK;AAC9B,UAAU,KAAK,EAAE,cAAc,CAAC,KAAK;AACrC,SAAS,EAAE,iBAAiB,EAAEE,gBAAU,CAAC,KAAK,GAAGA,gBAAU,CAAC,KAAK,CAAC,CAAC;AACnE,OAAO;AACP,MAAM,OAAO,QAAQ,CAAC,QAAQ,CAAC;AAC/B,KAAK,CAAC;AACN,GAAG;AACH,CAAC;;;;;"}