{"version": 3, "file": "useOption.js", "sources": ["../../../../../../packages/components/select-v2/src/useOption.ts"], "sourcesContent": ["import type { OptionV2EmitFn, OptionV2Props } from './defaults'\n\nexport function useOption(\n  props: OptionV2Props,\n  { emit }: { emit: OptionV2EmitFn }\n) {\n  return {\n    hoverItem: () => {\n      if (!props.disabled) {\n        emit('hover', props.index)\n      }\n    },\n    selectOptionClick: () => {\n      if (!props.disabled) {\n        emit('select', props.item, props.index)\n      }\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE;AAC3C,EAAE,OAAO;AACT,IAAI,SAAS,EAAE,MAAM;AACrB,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AAC3B,QAAQ,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACnC,OAAO;AACP,KAAK;AACL,IAAI,iBAAiB,EAAE,MAAM;AAC7B,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AAC3B,QAAQ,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAChD,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}