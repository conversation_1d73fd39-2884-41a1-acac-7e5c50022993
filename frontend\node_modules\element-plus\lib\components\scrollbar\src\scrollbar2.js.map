{"version": 3, "file": "scrollbar2.js", "sources": ["../../../../../../packages/components/scrollbar/src/scrollbar.vue"], "sourcesContent": ["<template>\n  <div ref=\"scrollbarRef\" :class=\"ns.b()\">\n    <div\n      ref=\"wrapRef\"\n      :class=\"wrapKls\"\n      :style=\"wrapStyle\"\n      :tabindex=\"tabindex\"\n      @scroll=\"handleScroll\"\n    >\n      <component\n        :is=\"tag\"\n        :id=\"id\"\n        ref=\"resizeRef\"\n        :class=\"resizeKls\"\n        :style=\"viewStyle\"\n        :role=\"role\"\n        :aria-label=\"ariaLabel\"\n        :aria-orientation=\"ariaOrientation\"\n      >\n        <slot />\n      </component>\n    </div>\n    <template v-if=\"!native\">\n      <bar ref=\"barRef\" :always=\"always\" :min-size=\"minSize\" />\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onActivated,\n  onMounted,\n  onUpdated,\n  provide,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { useEventListener, useResizeObserver } from '@vueuse/core'\nimport { addUnit, debugWarn, isNumber, isObject } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport Bar from './bar.vue'\nimport { scrollbarContextKey } from './constants'\nimport { scrollbarEmits, scrollbarProps } from './scrollbar'\nimport type { ScrollbarDirection } from './scrollbar'\nimport type { BarInstance } from './bar'\nimport type { CSSProperties, StyleValue } from 'vue'\n\nconst COMPONENT_NAME = 'ElScrollbar'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(scrollbarProps)\nconst emit = defineEmits(scrollbarEmits)\n\nconst ns = useNamespace('scrollbar')\n\nlet stopResizeObserver: (() => void) | undefined = undefined\nlet stopResizeListener: (() => void) | undefined = undefined\nlet wrapScrollTop = 0\nlet wrapScrollLeft = 0\nlet direction = '' as ScrollbarDirection\n\nconst scrollbarRef = ref<HTMLDivElement>()\nconst wrapRef = ref<HTMLDivElement>()\nconst resizeRef = ref<HTMLElement>()\nconst barRef = ref<BarInstance>()\n\nconst wrapStyle = computed<StyleValue>(() => {\n  const style: CSSProperties = {}\n  if (props.height) style.height = addUnit(props.height)\n  if (props.maxHeight) style.maxHeight = addUnit(props.maxHeight)\n  return [props.wrapStyle, style]\n})\n\nconst wrapKls = computed(() => {\n  return [\n    props.wrapClass,\n    ns.e('wrap'),\n    { [ns.em('wrap', 'hidden-default')]: !props.native },\n  ]\n})\n\nconst resizeKls = computed(() => {\n  return [ns.e('view'), props.viewClass]\n})\n\nconst handleScroll = () => {\n  if (wrapRef.value) {\n    barRef.value?.handleScroll(wrapRef.value)\n    const prevTop = wrapScrollTop\n    const prevLeft = wrapScrollLeft\n    wrapScrollTop = wrapRef.value.scrollTop\n    wrapScrollLeft = wrapRef.value.scrollLeft\n\n    const arrivedStates = {\n      bottom:\n        wrapScrollTop + wrapRef.value.clientHeight >=\n        wrapRef.value.scrollHeight,\n      top: wrapScrollTop <= 0 && prevTop !== 0,\n      right:\n        wrapScrollLeft + wrapRef.value.clientWidth >=\n          wrapRef.value.scrollWidth && prevLeft !== wrapScrollLeft,\n      left: wrapScrollLeft <= 0 && prevLeft !== 0,\n    }\n\n    if (prevTop !== wrapScrollTop) {\n      direction = wrapScrollTop > prevTop ? 'bottom' : 'top'\n    }\n    if (prevLeft !== wrapScrollLeft) {\n      direction = wrapScrollLeft > prevLeft ? 'right' : 'left'\n    }\n\n    emit('scroll', {\n      scrollTop: wrapScrollTop,\n      scrollLeft: wrapScrollLeft,\n    })\n    if (arrivedStates[direction]) emit('end-reached', direction)\n  }\n}\n\n// TODO: refactor method overrides, due to script setup dts\n// @ts-nocheck\nfunction scrollTo(xCord: number, yCord?: number): void\nfunction scrollTo(options: ScrollToOptions): void\nfunction scrollTo(arg1: unknown, arg2?: number) {\n  if (isObject(arg1)) {\n    wrapRef.value!.scrollTo(arg1)\n  } else if (isNumber(arg1) && isNumber(arg2)) {\n    wrapRef.value!.scrollTo(arg1, arg2)\n  }\n}\n\nconst setScrollTop = (value: number) => {\n  if (!isNumber(value)) {\n    debugWarn(COMPONENT_NAME, 'value must be a number')\n    return\n  }\n  wrapRef.value!.scrollTop = value\n}\n\nconst setScrollLeft = (value: number) => {\n  if (!isNumber(value)) {\n    debugWarn(COMPONENT_NAME, 'value must be a number')\n    return\n  }\n  wrapRef.value!.scrollLeft = value\n}\n\nconst update = () => {\n  barRef.value?.update()\n}\n\nwatch(\n  () => props.noresize,\n  (noresize) => {\n    if (noresize) {\n      stopResizeObserver?.()\n      stopResizeListener?.()\n    } else {\n      ;({ stop: stopResizeObserver } = useResizeObserver(resizeRef, update))\n      stopResizeListener = useEventListener('resize', update)\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => [props.maxHeight, props.height],\n  () => {\n    if (!props.native)\n      nextTick(() => {\n        update()\n        if (wrapRef.value) {\n          barRef.value?.handleScroll(wrapRef.value)\n        }\n      })\n  }\n)\n\nprovide(\n  scrollbarContextKey,\n  reactive({\n    scrollbarElement: scrollbarRef,\n    wrapElement: wrapRef,\n  })\n)\n\nonActivated(() => {\n  if (wrapRef.value) {\n    wrapRef.value.scrollTop = wrapScrollTop\n    wrapRef.value.scrollLeft = wrapScrollLeft\n  }\n})\n\nonMounted(() => {\n  if (!props.native)\n    nextTick(() => {\n      update()\n    })\n})\nonUpdated(() => update())\n\ndefineExpose({\n  /** @description scrollbar wrap ref */\n  wrapRef,\n  /** @description update scrollbar state manually */\n  update,\n  /** @description scrolls to a particular set of coordinates */\n  scrollTo,\n  /** @description set distance to scroll top */\n  setScrollTop,\n  /** @description set distance to scroll left */\n  setScrollLeft,\n  /** @description handle scroll event */\n  handleScroll,\n})\n</script>\n"], "names": ["useNamespace", "ref", "computed", "style", "addUnit", "isObject", "isNumber", "debugWarn", "watch", "useResizeObserver", "useEventListener", "nextTick", "provide", "scrollbarContextKey", "reactive", "onActivated", "onMounted", "onUpdated", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_createElementVNode"], "mappings": ";;;;;;;;;;;;;;;;;uCAoDc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAKA,mBAAa,WAAW,CAAA,CAAA;AAEnC,IAAA,IAAI,kBAA+C,GAAA,KAAA,CAAA,CAAA;AACnD,IAAA,IAAI,kBAA+C,GAAA,KAAA,CAAA,CAAA;AACnD,IAAA,IAAI,aAAgB,GAAA,CAAA,CAAA;AACpB,IAAA,IAAI,cAAiB,GAAA,CAAA,CAAA;AACrB,IAAA,IAAI,SAAY,GAAA,EAAA,CAAA;AAEhB,IAAA,MAAM,eAAeC,OAAoB,EAAA,CAAA;AACzC,IAAA,MAAM,UAAUA,OAAoB,EAAA,CAAA;AACpC,IAAA,MAAM,YAAYA,OAAiB,EAAA,CAAA;AACnC,IAAA,MAAM,SAASA,OAAiB,EAAA,CAAA;AAEhC,IAAM,MAAA,SAAA,GAAYC,aAAqB,MAAM;AAC3C,MAAA,MAAMC,UAAuB,EAAC,CAAA;AAC9B,MAAA,IAAI,MAAM,MAAQ;AAClB,QAAAA,cAAqB,GAAAC,aAAA,CAAA,KAAkB,CAAA,MAAA,CAAA,CAAA;AACvC,MAAO,IAAA,KAAO,CAAA,SAAA;AAAgB,QAC/BD,OAAA,CAAA,SAAA,GAAAC,aAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAED,MAAM,OAAA,CAAA,KAAA,CAAU,SAAS,EAAMD,OAAA,CAAA,CAAA;AAC7B,KAAO,CAAA,CAAA;AAAA,IAAA,MACC,OAAA,GAAAD,YAAA,CAAA,MAAA;AAAA,MACN,OAAK;AAAM,QACX,KAAG,CAAG,SAAG;AAA0C,QACrD,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,QACD,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,EAAA;AAED,OAAM,CAAA;AACJ,KAAA,CAAA,CAAA;AAAqC,IACvC,MAAC,SAAA,GAAAA,YAAA,CAAA,MAAA;AAED,MAAA,mBAAqB,CAAM,EAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AACzB,KAAA,CAAA,CAAA;AACE,IAAO,MAAA,YAAO,GAAa,MAAA;AAC3B,MAAA,IAAA,EAAA,CAAA;AACA,MAAA,IAAA,OAAiB,CAAA,KAAA,EAAA;AACjB,QAAA,CAAA,EAAA,GAAA,MAAA,CAAA,cAA8B,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAC9B,QAAA,MAAA,OAAA,GAAA,aAA+B,CAAA;AAE/B,QAAA,MAAM,QAAgB,GAAA,cAAA,CAAA;AAAA,QAAA,aAEF,GAAA,OAAA,CAAA,KAAA,CAAA,SAAc,CAAA;AAChB,QAChB,cAAsB,GAAA,OAAA,CAAA,KAAiB,CAAA,UAAA,CAAA;AAAA,QACvC,MAAA;AAE8C,UAC9C,MAAM,EAAkB,aAAA,GAAA,OAAkB,CAAA,KAAA,CAAA,YAAA,IAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AAAA,UAC5C,GAAA,EAAA,aAAA,IAAA,CAAA,IAAA,OAAA,KAAA,CAAA;AAEA,UAAA,qBAA+B,GAAA,OAAA,CAAA,KAAA,CAAA,WAAA,IAAA,OAAA,CAAA,KAAA,CAAA,WAAA,IAAA,QAAA,KAAA,cAAA;AAC7B,UAAY,IAAA,EAAA,cAAA,IAAA,CAAA,iBAAqC,CAAA;AAAA,SACnD,CAAA;AACA,QAAA,IAAI,yBAA6B,EAAA;AAC/B,UAAY,SAAA,GAAA,aAAA,GAAA,kBAAsC,GAAA,KAAA,CAAA;AAAA,SACpD;AAEA,QAAA,IAAA,QAAe,KAAA,cAAA,EAAA;AAAA,UACb,SAAW,GAAA,cAAA,GAAA,QAAA,GAAA,OAAA,GAAA,MAAA,CAAA;AAAA,SAAA;AACC,QACd,IAAC,CAAA,QAAA,EAAA;AACD,UAAA,SAAkB,EAAA,aAAA;AAAyC,UAC7D,UAAA,EAAA,cAAA;AAAA,SACF,CAAA,CAAA;AAMA,QAAS,IAAA,uBAAuC,CAAA;AAC9C,UAAI,IAAA,CAAA,aAAgB,EAAA,SAAA,CAAA,CAAA;AAClB,OAAQ;AAAoB,KAAA,CAC9B;AACE,IAAQ,SAAA,QAAA,CAAO,IAAS,EAAA,IAAA,EAAA;AAAU,MACpC,IAAAG,eAAA,CAAA,IAAA,CAAA,EAAA;AAAA,QACF,OAAA,CAAA,KAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AAEA,OAAM,MAAA,IAAAC,cAAe,CAAC,IAAkB,CAAA,IAAAA,cAAA,CAAA,IAAA,CAAA,EAAA;AACtC,QAAI,OAAU,CAAA,KAAA,CAAA,QAAQ,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AACpB,OAAA;AACA,KAAA;AAAA,IACF,MAAA,YAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAA,IAAA,CAAAA,eAAe,KAAY,CAAA,EAAA;AAAA,QAC7BC,eAAA,CAAA,cAAA,EAAA,wBAAA,CAAA,CAAA;AAEA,QAAM,OAAA;AACJ,OAAI;AACF,MAAA,OAAA,CAAA,uBAAkD,CAAA;AAClD,KAAA,CAAA;AAAA,IACF,MAAA,aAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAA,IAAA,CAAAD,eAAe,KAAa,CAAA,EAAA;AAAA,QAC9BC,eAAA,CAAA,cAAA,EAAA,wBAAA,CAAA,CAAA;AAEA,QAAA;AACE,OAAA;AAAqB,MACvB,OAAA,CAAA,KAAA,CAAA,UAAA,GAAA,KAAA,CAAA;AAEA,KAAA,CAAA;AAAA,IAAA,YACc,GAAA,MAAA;AAAA,MACZ,IAAc,EAAA,CAAA;AACZ,MAAA,CAAA,EAAA,GAAI,MAAU,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AACZ,KAAqB,CAAA;AACrB,IAAqBC,SAAA,CAAA,MAAA,KAAA,CAAA,QAAA,EAAA,CAAA,QAAA,KAAA;AAAA,MAAA,IAChB,QAAA,EAAA;AACL,QAAA,kBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,kBAAA,EAAA,CAAA;AAAC,QAAA,kBAAS,IAAA,IAAA,GAAA,KAAuB,CAAA,GAAA;AACjC,OAAqB,MAAA;AACvB,QACF,CAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,GAAAC,sBAAA,CAAA,SAAA,EAAA,MAAA,CAAA,EAAA;AAAA,QACE,kBAAgB,GAAAC,qBAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;AAAA,OACpB;AAEA,KAAA,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IAAAF,SACQ,CAAA,MAAO,CAAA,KAAA,CAAA,gBAAuB,CAAA,MAAA,CAAA,EAAA,MAAA;AAAA,MACpC,IAAM,CAAA,KAAA,CAAA,MAAA;AACJ,QAAAG,YAAW,CAAA,MAAA;AACT,UAAA,IAAA,EAAA,CAAA;AACE,UAAO,MAAA,EAAA,CAAA;AACP,UAAA,IAAA,aAAmB,EAAA;AACjB,YAAO,CAAA,EAAA,GAAA,MAAA,CAAA,KAAoB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAa,EAAA,CAAA,YAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAAA,WAC1C;AAAA,SAAA,CACF,CAAC;AAAA,KACL,CAAA,CAAA;AAAA,IACFC,WAAA,CAAAC,6BAAA,EAAAC,YAAA,CAAA;AAEA,MAAA,gBAAA,EAAA,YAAA;AAAA,MACE,WAAA,EAAA,OAAA;AAAA,KAAA,CACA,CAAS,CAAA;AAAA,IAAAC,eACW,CAAA,MAAA;AAAA,MAAA,IACL,OAAA,CAAA,KAAA,EAAA;AAAA,QACd,OAAA,CAAA,KAAA,CAAA,SAAA,GAAA,aAAA,CAAA;AAAA,QACH,OAAA,CAAA,KAAA,CAAA,UAAA,GAAA,cAAA,CAAA;AAEA,OAAA;AACE,KAAA,CAAA,CAAA;AACE,IAAAC,aAAA,CAAA;AACA,MAAA,IAAA,CAAA,KAAQ,OAAmB;AAAA,QAC7BL,YAAA,CAAA,MAAA;AAAA,UACD,MAAA,EAAA,CAAA;AAED,SAAA,CAAA,CAAA;AACE,KAAA,CAAA,CAAA;AACE,IAAAM,aAAA,CAAA,MAAe,MAAA,EAAA,CAAA,CAAA;AACb,IAAO,MAAA,CAAA;AAAA,MAAA,OACR;AAAA,MACJ,MAAA;AACD,MAAU,QAAA;AAEV,MAAa,YAAA;AAAA,MAAA,aAAA;AAAA,MAEX,YAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAEA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QAEA,OAAA,EAAA,cAAA;AAAA,QAAA,GAAA,EAAA,YAAA;AAAA,QAEA,KAAA,EAAAC,kBAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAEAC,sBAAA,CAAA,KAAA,EAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAEA,GAAA,EAAA,OAAA;AAAA,UACD,KAAA,EAAAF,kBAAA,CAAAC,SAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}