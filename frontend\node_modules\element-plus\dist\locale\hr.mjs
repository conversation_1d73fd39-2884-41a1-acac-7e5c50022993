/*! Element Plus v2.10.1 */

var hr = {
  name: "hr",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "O\u010Disti"
    },
    datepicker: {
      now: "<PERSON>a",
      today: "<PERSON><PERSON>",
      cancel: "Otka\u017Ei",
      clear: "O\u010Disti",
      confirm: "OK",
      selectDate: "Odaberi datum",
      selectTime: "Odaberi vrijeme",
      startDate: "Datum po\u010Detka",
      startTime: "Vrijeme po\u010Detka",
      endDate: "Datum zavr\u0161etka",
      endTime: "Vrijeme zavr\u0161etka",
      prevYear: "Prethodna godina",
      nextYear: "Sljede\u0107a godina",
      prevMonth: "Prethodni mjesec",
      nextMonth: "Sljede\u0107i mjesec",
      year: "",
      month1: "Sije\u010Danj",
      month2: "Velja\u010Da",
      month3: "O\u017Eujak",
      month4: "Travanj",
      month5: "Svibanj",
      month6: "Lipanj",
      month7: "Srpanj",
      month8: "Kolovoz",
      month9: "Rujan",
      month10: "Listopad",
      month11: "Studeni",
      month12: "Prosinac",
      week: "tjedan",
      weeks: {
        sun: "Ned",
        mon: "Pon",
        tue: "Uto",
        wed: "Sri",
        thu: "\u010Cet",
        fri: "Pet",
        sat: "Sub"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "May",
        jun: "Jun",
        jul: "Jul",
        aug: "Aug",
        sep: "Sep",
        oct: "Oct",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "U\u010Ditavanje",
      noMatch: "Nema prona\u0111enih podataka",
      noData: "Nema podataka",
      placeholder: "Izaberi"
    },
    mention: {
      loading: "U\u010Ditavanje"
    },
    cascader: {
      noMatch: "Nema prona\u0111enih podataka",
      loading: "U\u010Ditavanje",
      placeholder: "Izaberi",
      noData: "Nema podataka"
    },
    pagination: {
      goto: "Idi na",
      pagesize: "/stranica",
      total: "Ukupno {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Poruka",
      confirm: "OK",
      cancel: "Otka\u017Ei",
      error: "Pogre\u0161an unos"
    },
    upload: {
      deleteTip: "pritisnite izbri\u0161i za brisanje",
      delete: "Izbri\u0161i",
      preview: "Pregled",
      continue: "Nastavak"
    },
    table: {
      emptyText: "Nema podataka",
      confirmFilter: "Potvrdi",
      resetFilter: "Resetiraj",
      clearFilter: "Sve",
      sumText: "Suma"
    },
    tree: {
      emptyText: "Nema podataka"
    },
    transfer: {
      noMatch: "Nema prona\u0111enih podataka",
      noData: "Nema podataka",
      titles: ["Lista 1", "Lista 2"],
      filterPlaceholder: "Unesite klju\u010Dnu rije\u010D",
      noCheckedFormat: "{total} stavki",
      hasCheckedFormat: "{checked}/{total} checked"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { hr as default };
