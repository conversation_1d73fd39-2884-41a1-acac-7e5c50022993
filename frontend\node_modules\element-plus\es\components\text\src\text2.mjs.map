{"version": 3, "file": "text2.mjs", "sources": ["../../../../../../packages/components/text/src/text.vue"], "sourcesContent": ["<template>\n  <component\n    :is=\"tag\"\n    ref=\"textRef\"\n    :class=\"textKls\"\n    :style=\"{ '-webkit-line-clamp': lineClamp }\"\n  >\n    <slot />\n  </component>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onMounted, onUpdated, ref, useAttrs } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormSize } from '@element-plus/components/form'\nimport { isUndefined } from '@element-plus/utils'\nimport { textProps } from './text'\n\ndefineOptions({\n  name: 'ElText',\n})\n\nconst props = defineProps(textProps)\nconst textRef = ref<HTMLElement>()\n\nconst textSize = useFormSize()\nconst ns = useNamespace('text')\n\nconst textKls = computed(() => [\n  ns.b(),\n  ns.m(props.type),\n  ns.m(textSize.value),\n  ns.is('truncated', props.truncated),\n  ns.is('line-clamp', !isUndefined(props.lineClamp)),\n])\n\nconst bindTitle = () => {\n  const inheritTitle = useAttrs().title\n\n  if (inheritTitle) return\n  let shouldAddTitle = false\n  const text = textRef.value?.textContent || ''\n\n  if (props.truncated) {\n    const width = textRef.value?.offsetWidth\n    const scrollWidth = textRef.value?.scrollWidth\n    if (width && scrollWidth && scrollWidth > width) {\n      shouldAddTitle = true\n    }\n  } else if (!isUndefined(props.lineClamp)) {\n    const height = textRef.value?.offsetHeight\n    const scrollHeight = textRef.value?.scrollHeight\n    if (height && scrollHeight && scrollHeight > height) {\n      shouldAddTitle = true\n    }\n  }\n\n  if (shouldAddTitle) {\n    textRef.value?.setAttribute('title', text)\n  } else {\n    textRef.value?.removeAttribute('title')\n  }\n}\n\nonMounted(bindTitle)\nonUpdated(bindTitle)\n</script>\n"], "names": [], "mappings": ";;;;;;;mCAkBc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAGA,IAAA,MAAM,UAAU,GAAiB,EAAA,CAAA;AAEjC,IAAA,MAAM,WAAW,WAAY,EAAA,CAAA;AAC7B,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAE9B,IAAM,MAAA,OAAA,GAAU,SAAS,MAAM;AAAA,MAC7B,GAAG,CAAE,EAAA;AAAA,MACL,EAAA,CAAG,CAAE,CAAA,KAAA,CAAM,IAAI,CAAA;AAAA,MACf,EAAA,CAAG,CAAE,CAAA,QAAA,CAAS,KAAK,CAAA;AAAA,MACnB,EAAG,CAAA,EAAA,CAAG,WAAa,EAAA,KAAA,CAAM,SAAS,CAAA;AAAA,MAClC,GAAG,EAAG,CAAA,YAAA,EAAc,CAAC,WAAY,CAAA,KAAA,CAAM,SAAS,CAAC,CAAA;AAAA,KAClD,CAAA,CAAA;AAED,IAAA,MAAM,YAAY,MAAM;AACtB,MAAM,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,UAAwB,CAAE;AAEhC,MAAA,MAAkB,YAAA,GAAA,QAAA,EAAA,CAAA,KAAA,CAAA;AAClB,MAAA,IAAI,YAAiB;AACrB,QAAM,OAAA;AAEN,MAAA,IAAI,cAAiB,GAAA,KAAA,CAAA;AACnB,MAAM,MAAA,IAAA,GAAA,CAAA,CAAA,YAAuB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,KAAA,EAAA,CAAA;AAC7B,MAAM,IAAA,KAAA,CAAA,SAAA,EAAA;AACN,QAAI,MAAA,KAAA,GAAwB,CAAA,EAAA,GAAA,OAAA,CAAA,KAAA,KAAA,IAAc,GAAO,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,CAAA;AAC/C,QAAiB,MAAA,WAAA,GAAA,CAAA,EAAA,GAAA,OAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,CAAA;AAAA,QACnB,IAAA,KAAA,IAAA,WAAA,IAAA,WAAA,GAAA,KAAA,EAAA;AAAA,UACS,cAAa,GAAA,IAAA,CAAA;AACtB,SAAM;AACN,OAAM,MAAA,IAAA,CAAA,WAAe,gBAAe,CAAA,EAAA;AACpC,QAAI,MAAA,MAAA,GAA0B,CAAA,EAAA,GAAA,OAAA,CAAA,KAAA,KAAA,IAAA,GAAuB,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA;AACnD,QAAiB,MAAA,YAAA,GAAA,CAAA,EAAA,GAAA,OAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA;AAAA,QACnB,IAAA,MAAA,IAAA,YAAA,IAAA,YAAA,GAAA,MAAA,EAAA;AAAA,UACF,cAAA,GAAA,IAAA,CAAA;AAEA,SAAA;AACE,OAAQ;AAAiC,MAC3C,IAAO,cAAA,EAAA;AACL,QAAQ,CAAA,EAAA,GAAA,OAAA,CAAA,iBAAuB,KAAO,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,OAAA,EAAA,IAAA,CAAA,CAAA;AAAA,OACxC,MAAA;AAAA,QACF,CAAA,EAAA,GAAA,OAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAA,CAAA,OAAA,CAAA,CAAA;AAEA,OAAA;AACA,KAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;"}