{"version": 3, "file": "trigger.js", "sources": ["../../../../../../packages/components/popper/src/trigger.vue"], "sourcesContent": ["<template>\n  <el-only-child\n    v-if=\"!virtualTriggering\"\n    v-bind=\"$attrs\"\n    :aria-controls=\"ariaControls\"\n    :aria-describedby=\"ariaDescribedby\"\n    :aria-expanded=\"ariaExpanded\"\n    :aria-haspopup=\"ariaHaspopup\"\n  >\n    <slot />\n  </el-only-child>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, onMounted, watch } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { unrefElement } from '@vueuse/core'\nimport { ElOnlyChild } from '@element-plus/components/slot'\nimport { useForwardRef } from '@element-plus/hooks'\nimport { isElement, isFocusable } from '@element-plus/utils'\nimport { POPPER_INJECTION_KEY } from './constants'\nimport { popperTriggerProps } from './trigger'\n\nimport type { WatchStopHandle } from 'vue'\n\ndefineOptions({\n  name: 'ElPopperTrigger',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(popperTriggerProps)\n\nconst { role, triggerRef } = inject(POPPER_INJECTION_KEY, undefined)!\n\nuseForwardRef(triggerRef)\n\nconst ariaControls = computed<string | undefined>(() => {\n  return ariaHaspopup.value ? props.id : undefined\n})\n\nconst ariaDescribedby = computed<string | undefined>(() => {\n  if (role && role.value === 'tooltip') {\n    return props.open && props.id ? props.id : undefined\n  }\n  return undefined\n})\n\nconst ariaHaspopup = computed<string | undefined>(() => {\n  if (role && role.value !== 'tooltip') {\n    return role.value\n  }\n  return undefined\n})\n\nconst ariaExpanded = computed<string | undefined>(() => {\n  return ariaHaspopup.value ? `${props.open}` : undefined\n})\n\nlet virtualTriggerAriaStopWatch: WatchStopHandle | undefined = undefined\n\nconst TRIGGER_ELE_EVENTS = [\n  'onMouseenter',\n  'onMouseleave',\n  'onClick',\n  'onKeydown',\n  'onFocus',\n  'onBlur',\n  'onContextmenu',\n] as const\n\nonMounted(() => {\n  watch(\n    () => props.virtualRef,\n    (virtualEl) => {\n      if (virtualEl) {\n        triggerRef.value = unrefElement(virtualEl as HTMLElement)\n      }\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  watch(\n    triggerRef,\n    (el, prevEl) => {\n      virtualTriggerAriaStopWatch?.()\n      virtualTriggerAriaStopWatch = undefined\n      if (isElement(el)) {\n        TRIGGER_ELE_EVENTS.forEach((eventName) => {\n          const handler = props[eventName]\n          if (handler) {\n            ;(el as HTMLElement).addEventListener(\n              eventName.slice(2).toLowerCase(),\n              handler\n            )\n            ;(prevEl as HTMLElement)?.removeEventListener?.(\n              eventName.slice(2).toLowerCase(),\n              handler\n            )\n          }\n        })\n        if (isFocusable(el as HTMLElement)) {\n          virtualTriggerAriaStopWatch = watch(\n            [ariaControls, ariaDescribedby, ariaHaspopup, ariaExpanded],\n            (watches) => {\n              ;[\n                'aria-controls',\n                'aria-describedby',\n                'aria-haspopup',\n                'aria-expanded',\n              ].forEach((key, idx) => {\n                isNil(watches[idx])\n                  ? el.removeAttribute(key)\n                  : el.setAttribute(key, watches[idx]!)\n              })\n            },\n            { immediate: true }\n          )\n        }\n      }\n      if (isElement(prevEl) && isFocusable(prevEl as HTMLElement)) {\n        ;[\n          'aria-controls',\n          'aria-describedby',\n          'aria-haspopup',\n          'aria-expanded',\n        ].forEach((key) => prevEl.removeAttribute(key))\n      }\n    },\n    {\n      immediate: true,\n    }\n  )\n})\n\nonBeforeUnmount(() => {\n  virtualTriggerAriaStopWatch?.()\n  virtualTriggerAriaStopWatch = undefined\n  if (triggerRef.value && isElement(triggerRef.value)) {\n    const el = triggerRef.value as HTMLElement\n    TRIGGER_ELE_EVENTS.forEach((eventName) => {\n      const handler = props[eventName]\n      if (handler) {\n        el.removeEventListener(eventName.slice(2).toLowerCase(), handler)\n      }\n    })\n    triggerRef.value = undefined\n  }\n})\n\ndefineExpose({\n  /**\n   * @description trigger element\n   */\n  triggerRef,\n})\n</script>\n"], "names": ["inject", "POPPER_INJECTION_KEY", "useForwardRef", "computed", "onMounted", "watch", "unrefElement", "isElement", "isFocusable", "isNil", "onBeforeUnmount", "_openBlock", "_createBlock", "_unref", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_mergeProps", "_withCtx", "_renderSlot", "_createCommentVNode", "_export_sfc"], "mappings": ";;;;;;;;;;;;;;;uCAyBc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAIA,IAAA,MAAM,EAAE,IAAM,EAAA,UAAA,EAAe,GAAAA,UAAA,CAAOC,gCAAsB,KAAS,CAAA,CAAA,CAAA;AAEnE,IAAAC,mBAAA,CAAc,UAAU,CAAA,CAAA;AAExB,IAAM,MAAA,YAAA,GAAeC,aAA6B,MAAM;AACtD,MAAO,OAAA,YAAA,CAAa,KAAQ,GAAA,KAAA,CAAM,EAAK,GAAA,KAAA,CAAA,CAAA;AAAA,KACxC,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkBA,aAA6B,MAAM;AACzD,MAAI,IAAA,IAAA,IAAQ,IAAK,CAAA,KAAA,KAAU,SAAW,EAAA;AACpC,QAAA,OAAO,KAAM,CAAA,IAAA,IAAQ,KAAM,CAAA,EAAA,GAAK,MAAM,EAAK,GAAA,KAAA,CAAA,CAAA;AAAA,OAC7C;AACA,MAAO,OAAA,KAAA,CAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAeA,aAA6B,MAAM;AACtD,MAAI,IAAA,IAAA,IAAQ,IAAK,CAAA,KAAA,KAAU,SAAW,EAAA;AACpC,QAAA,OAAO,IAAK,CAAA,KAAA,CAAA;AAAA,OACd;AACA,MAAO,OAAA,KAAA,CAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAeA,aAA6B,MAAM;AACtD,MAAA,OAAO,YAAa,CAAA,KAAA,GAAQ,CAAG,EAAA,KAAA,CAAM,IAAI,CAAK,CAAA,GAAA,KAAA,CAAA,CAAA;AAAA,KAC/C,CAAA,CAAA;AAED,IAAA,IAAI,2BAA2D,GAAA,KAAA,CAAA,CAAA;AAE/D,IAAA,MAAM,kBAAqB,GAAA;AAAA,MACzB,cAAA;AAAA,MACA,cAAA;AAAA,MACA,SAAA;AAAA,MACA,WAAA;AAAA,MACA,SAAA;AAAA,MACA,QAAA;AAAA,MACA,eAAA;AAAA,KACF,CAAA;AAEA,IAAAC,aAAA,CAAU,MAAM;AACd,MAAAC,SAAA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,CAAA,SAAA,KAAA;AAAA,QACE,aAAY,EAAA;AAAA,UACG,UAAA,CAAA,KAAA,GAAAC,iBAAA,CAAA,SAAA,CAAA,CAAA;AACb,SAAA;AACE,OAAW,EAAA;AAA6C,QAC1D,SAAA,EAAA,IAAA;AAAA,OACF,CAAA,CAAA;AAAA,MACAD,SAAA,CAAA,UAAA,EAAA,CAAA,EAAA,EAAA,MAAA,KAAA;AAAA,QAAA,2BACa,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,2BAAA,EAAA,CAAA;AAAA,QACb,2BAAA,GAAA,KAAA,CAAA,CAAA;AAAA,QACF,IAAAE,eAAA,CAAA,EAAA,CAAA,EAAA;AAEA,UAAA,kBAAA,CAAA,OAAA,CAAA,CAAA,SAAA,KAAA;AAAA,YACE,IAAA,EAAA,CAAA;AAAA,kBACgB,OAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AACd,YAA8B,IAAA,OAAA,EAAA;AAE9B,cAAI,EAAA,CAAA,gBAAe,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,EAAA,EAAA,OAAA,CAAA,CAAA;AACjB,cAAmB,CAAA,EAAA,GAAA,MAAA,IAAA,IAAA,GAAA,KAAuB,CAAA,GAAA,MAAA,CAAA,mBAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,EAAA,EAAA,OAAA,CAAA,CAAA;AACxC,aAAM;AACN,WAAA,CAAA,CAAA;AACE,UAAA,IAAAC,gBAAA,CAAA,EAAA,CAAA,EAAA;AAAC,YAAA,2BAAoB,GAAAH,SAAA,CAAA,CAAA,YAAA,EAAA,eAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,CAAA,OAAA,KAAA;AACY,cAC/B;AAAA,gBACF,eAAA;AACC,gBAAC,kBAAwB;AAAA,gBAAA,eACd;AAAqB,gBAC/B,eAAA;AAAA,eACF,CAAA,OAAA,CAAA,CAAA,GAAA,EAAA,GAAA,KAAA;AAAA,gBACFI,mBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,EAAA,CAAA,eAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,GAAA,EAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,eACD,CAAA,CAAA;AACD,aAAI,EAAA,EAAA,SAAA,EAAY,IAAoB,EAAA,CAAA,CAAA;AAClC,WAA8B;AAAA,SAAA;AAC8B,QAAA,IAAAF,eAC7C,CAAA,MAAA,CAAA,IAAAC,gBAAA,CAAA,MAAA,CAAA,EAAA;AACV,UAAA;AAAA,YACC,eAAA;AAAA,YACA,kBAAA;AAAA,YACA,eAAA;AAAA,YACA,eAAA;AAAA,WAAA,CAAA,OACA,CAAA,CAAA,GAAA,KAAS,MAAa,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACtB,SAAA;AAEsC,OAAA,EAAA;AACvC,QACH,SAAA,EAAA,IAAA;AAAA,OACA,CAAA,CAAA;AAAkB,KACpB,CAAA,CAAA;AAAA,IACFE,mBAAA,CAAA,MAAA;AAAA,MACF,2BAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,2BAAA,EAAA,CAAA;AACA,MAAA,2BAAyB,GAAA,KAAA,CAAA,CAAA;AACvB,MAAA,IAAA,UAAA,CAAA,KAAA,IAAAH,eAAA,CAAA,UAAA,CAAA,KAAA,CAAA,EAAA;AAAC,QAAA,MAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,QACC,kBAAA,CAAA,OAAA,CAAA,CAAA,SAAA,KAAA;AAAA,UACA,MAAA,OAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAAA,UACA,IAAA,OAAA,EAAA;AAAA,YACA,EAAA,CAAA,mBAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,EAAA,EAAA,OAAA,CAAA,CAAA;AAAA,WAAA;AAC4C,SAChD,CAAA,CAAA;AAAA,QACF,UAAA,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAAA,OACA;AAAA,KAAA,CAAA,CAAA;AACa,IACb,MAAA,CAAA;AAAA,MACF,UAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAA,OAAA,CAAA,IAAA,EAAA,MAAsB,KAAA;AACpB,MAA8B,OAAA,CAAA,IAAA,CAAA,iBAAA,IAAAI,aAAA,EAAA,EAAAC,eAAA,CAAAC,SAAA,CAAAC,mBAAA,CAAA,EAAAC,cAAA,CAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA,IAAA,CAAA,MAAA,EAAA;AAC9B,QAA8B,eAAA,EAAAF,SAAA,CAAA,YAAA,CAAA;AAC9B,QAAA,kBAAe,EAAAA,SAAmB,CAAA,eAAA,CAAW;AAC3C,QAAA,eAAsB,EAAAA,SAAA,CAAA,YAAA,CAAA;AACtB,QAAmB,eAAA,EAAAA,SAAA,CAAA,YAAuB,CAAA;AACxC,OAAM,CAAA,EAAA;AACN,QAAA,OAAa,EAAAG,WAAA,CAAA,MAAA;AACX,UAAAC,4BAAuB,SAAU,CAAA;AAA+B,SAClE,CAAA;AAAA,QACF,CAAC,EAAA,CAAA;AACD,OAAA,EAAA,EAAA,EAAA,CAAA,eAAmB,EAAA,kBAAA,EAAA,eAAA,EAAA,eAAA,CAAA,CAAA,IAAAC,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA,CAAA;AAAA,KACrB,CAAA;AAAA,GAAA;AAGF,CAAa,CAAA,CAAA;AAAA,sBAAA,gBAAAC,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,aAAA,CAAA,CAAA,CAAA;;;;"}