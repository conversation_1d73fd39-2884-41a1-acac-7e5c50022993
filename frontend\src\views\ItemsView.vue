<template>
  <div class="items-view">
    <div class="page-header">
      <div class="header-left">
        <h1>商品管理</h1>
        <p>管理所有租赁商品</p>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          :icon="Plus"
          @click="showAddDialog = true"
        >
          添加商品
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="商品名称">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入商品名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select
            v-model="searchForm.category"
            placeholder="请选择分类"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="category in categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="可租用" value="AVAILABLE" />
            <el-option label="租赁中" value="RENTED" />
            <el-option label="维护中" value="MAINTENANCE" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 商品列表 -->
    <div class="items-grid">
      <el-row :gutter="20">
        <el-col
          v-for="item in itemsStore.items"
          :key="item.id"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          class="item-col"
        >
          <el-card class="item-card" :body-style="{ padding: '0' }">
            <div class="item-image">
              <img :src="item.imageUrl" :alt="item.name" />
              <div class="status-badge" :class="getStatusClass(item.status)">
                {{ getStatusText(item.status) }}
              </div>
            </div>
            
            <div class="item-content">
              <h3 class="item-name">{{ item.name }}</h3>
              <div class="item-category">
                <el-tag size="small" type="info">{{ item.category }}</el-tag>
              </div>
              <p class="item-description">{{ item.description }}</p>
              
              <div class="item-footer">
                <div class="price-info">
                  <span class="price">¥{{ item.dailyPrice.toFixed(2) }}</span>
                  <span class="unit">/天</span>
                </div>
                <div class="stock-info">
                  <el-icon><Box /></el-icon>
                  <span>库存: {{ item.stock }}</span>
                </div>
              </div>
              
              <div class="item-actions">
                <el-button
                  size="small"
                  type="primary"
                  :icon="Edit"
                  @click="handleEdit(item)"
                >
                  编辑
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  :icon="Delete"
                  @click="handleDelete(item)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="itemsStore.currentPage"
        v-model:page-size="itemsStore.pageSize"
        :total="itemsStore.total"
        :page-sizes="[12, 24, 48, 96]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑商品对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingItem ? '编辑商品' : '添加商品'"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="itemFormRef"
        :model="itemForm"
        :rules="itemFormRules"
        label-width="80px"
      >
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="itemForm.name" placeholder="请输入商品名称" />
        </el-form-item>
        
        <el-form-item label="分类" prop="category">
          <el-select v-model="itemForm.category" placeholder="请选择分类" style="width: 100%">
            <el-option
              v-for="category in categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="itemForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入商品描述"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日租价格" prop="dailyPrice">
              <el-input-number
                v-model="itemForm.dailyPrice"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库存数量" prop="stock">
              <el-input-number
                v-model="itemForm.stock"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="状态" prop="status">
          <el-select v-model="itemForm.status" style="width: 100%">
            <el-option label="可租用" value="AVAILABLE" />
            <el-option label="租赁中" value="RENTED" />
            <el-option label="维护中" value="MAINTENANCE" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="商品图片">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleImageUpload"
          >
            <img v-if="itemForm.imageUrl" :src="itemForm.imageUrl" class="uploaded-image" />
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button
          type="primary"
          :loading="itemsStore.loading"
          @click="handleSave"
        >
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import type { FormInstance, FormRules, UploadProps } from 'element-plus'
import { Plus, Edit, Delete, Box } from '@element-plus/icons-vue'
import { useItemsStore } from '@/stores/items'
import type { Item, ItemForm } from '@/types'

const itemsStore = useItemsStore()

// 对话框状态
const showAddDialog = ref(false)
const editingItem = ref<Item | null>(null)

// 表单引用
const itemFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: '',
  status: ''
})

// 商品表单
const itemForm = reactive<ItemForm>({
  name: '',
  description: '',
  dailyPrice: 0,
  stock: 0,
  category: '',
  status: 'AVAILABLE',
  imageUrl: ''
})

// 商品分类
const categories = ref([
  '电子产品',
  '摄影器材',
  '无人机',
  '工具',
  '户外装备',
  '交通工具'
])

// 表单验证规则
const itemFormRules: FormRules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '商品名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择商品分类', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入商品描述', trigger: 'blur' },
    { min: 10, max: 200, message: '商品描述长度在 10 到 200 个字符', trigger: 'blur' }
  ],
  dailyPrice: [
    { required: true, message: '请输入日租价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '日租价格必须大于0', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: '请输入库存数量', trigger: 'blur' },
    { type: 'number', min: 0, message: '库存数量不能小于0', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择商品状态', trigger: 'change' }
  ]
}

// 获取状态样式类
const getStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    AVAILABLE: 'available',
    RENTED: 'rented',
    MAINTENANCE: 'maintenance'
  }
  return classMap[status] || ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    AVAILABLE: '可租用',
    RENTED: '租赁中',
    MAINTENANCE: '维护中'
  }
  return textMap[status] || status
}

// 搜索
const handleSearch = () => {
  itemsStore.currentPage = 1
  loadItems()
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.category = ''
  searchForm.status = ''
  handleSearch()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  itemsStore.pageSize = size
  loadItems()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  itemsStore.currentPage = page
  loadItems()
}

// 编辑商品
const handleEdit = (item: Item) => {
  editingItem.value = item
  Object.assign(itemForm, {
    name: item.name,
    description: item.description,
    dailyPrice: item.dailyPrice,
    stock: item.stock,
    category: item.category,
    status: item.status,
    imageUrl: item.imageUrl
  })
  showAddDialog.value = true
}

// 删除商品
const handleDelete = (item: Item) => {
  ElMessageBox.confirm(
    `确定要删除商品"${item.name}"吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    await itemsStore.deleteItem(item.id)
    loadItems()
  })
}

// 对话框关闭
const handleDialogClose = () => {
  editingItem.value = null
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(itemForm, {
    name: '',
    description: '',
    dailyPrice: 0,
    stock: 0,
    category: '',
    status: 'AVAILABLE',
    imageUrl: ''
  })
  itemFormRef.value?.resetFields()
}

// 保存商品
const handleSave = async () => {
  if (!itemFormRef.value) return

  await itemFormRef.value.validate(async (valid) => {
    if (valid) {
      let success = false

      if (editingItem.value) {
        // 编辑
        success = await itemsStore.updateItem(editingItem.value.id, itemForm)
      } else {
        // 添加
        success = await itemsStore.addItem(itemForm)
      }

      if (success) {
        showAddDialog.value = false
        loadItems()
      }
    }
  })
}

// 图片上传前验证
const beforeImageUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 处理图片上传
const handleImageUpload = (options: any) => {
  // TODO: 实现图片上传到服务器
  // 临时使用文件预览
  const file = options.file
  const reader = new FileReader()
  reader.onload = (e) => {
    itemForm.imageUrl = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

// 加载商品列表
const loadItems = () => {
  const query = {
    current: itemsStore.currentPage,
    size: itemsStore.pageSize,
    keyword: searchForm.keyword || undefined,
    category: searchForm.category || undefined,
    status: searchForm.status || undefined
  }
  itemsStore.fetchItems(query)
}

onMounted(() => {
  loadItems()
})
</script>

<style lang="scss" scoped>
.items-view {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-left {
      h1 {
        margin: 0 0 8px;
        color: var(--dark-color);
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #666;
      }
    }
  }

  .search-card {
    margin-bottom: 24px;
    border: none;
    box-shadow: var(--box-shadow);
  }

  .items-grid {
    margin-bottom: 24px;

    .item-col {
      margin-bottom: 20px;
    }

    .item-card {
      height: 100%;
      border: none;
      box-shadow: var(--box-shadow);
      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }
    }

    .item-image {
      position: relative;
      height: 200px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.05);
      }

      .status-badge {
        position: absolute;
        top: 12px;
        right: 12px;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;

        &.available {
          background-color: rgba(46, 196, 182, 0.9);
          color: white;
        }

        &.rented {
          background-color: rgba(255, 159, 28, 0.9);
          color: white;
        }

        &.maintenance {
          background-color: rgba(231, 29, 54, 0.9);
          color: white;
        }
      }
    }

    .item-content {
      padding: 16px;

      .item-name {
        margin: 0 0 8px;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark-color);
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .item-category {
        margin-bottom: 8px;
      }

      .item-description {
        margin: 0 0 16px;
        color: #666;
        font-size: 0.9rem;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .item-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .price-info {
          .price {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
          }

          .unit {
            font-size: 0.9rem;
            color: #666;
          }
        }

        .stock-info {
          display: flex;
          align-items: center;
          font-size: 0.9rem;
          color: #666;

          .el-icon {
            margin-right: 4px;
          }
        }
      }

      .item-actions {
        display: flex;
        gap: 8px;

        .el-button {
          flex: 1;
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
}

// 图片上传样式
.image-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;

    &:hover {
      border-color: var(--primary-color);
    }
  }

  .image-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    text-align: center;
    line-height: 120px;
  }

  .uploaded-image {
    width: 120px;
    height: 120px;
    display: block;
    object-fit: cover;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .items-view {
    .page-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
    }

    .search-card {
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 16px;

          .el-input,
          .el-select {
            width: 100% !important;
          }
        }
      }
    }

    .item-card {
      .item-image {
        height: 160px;
      }

      .item-content {
        padding: 12px;

        .item-name {
          font-size: 1rem;
        }

        .item-description {
          font-size: 0.85rem;
        }

        .item-footer {
          .price-info .price {
            font-size: 1.1rem;
          }

          .stock-info {
            font-size: 0.85rem;
          }
        }
      }
    }
  }
}
</style>
