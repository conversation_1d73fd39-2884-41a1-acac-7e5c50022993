<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>租赁系统 API 测试</h1>
    
    <div class="test-section">
        <h2>1. 测试登录</h2>
        <button onclick="testLogin()">测试管理员登录</button>
        <button onclick="testUserLogin()">测试普通用户登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 测试商品列表</h2>
        <button onclick="testItems()">获取商品列表</button>
        <div id="itemsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 测试用户信息</h2>
        <button onclick="testUserInfo()">获取用户信息</button>
        <div id="userInfoResult" class="result"></div>
    </div>

    <script>
        let authToken = '';
        
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.data.token;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `登录成功！\nToken: ${authToken.substring(0, 50)}...\nUser: ${JSON.stringify(data.data.user, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `登录失败: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败: ${error.message}`;
            }
        }
        
        async function testUserLogin() {
            const resultDiv = document.getElementById('loginResult');
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'user',
                        password: 'user123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.data.token;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `登录成功！\nToken: ${authToken.substring(0, 50)}...\nUser: ${JSON.stringify(data.data.user, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `登录失败: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败: ${error.message}`;
            }
        }
        
        async function testItems() {
            const resultDiv = document.getElementById('itemsResult');
            try {
                const response = await fetch('http://localhost:8080/api/items', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `获取商品列表成功！\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `获取商品列表失败: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败: ${error.message}`;
            }
        }
        
        async function testUserInfo() {
            const resultDiv = document.getElementById('userInfoResult');
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请先登录获取Token';
                return;
            }
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/user', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `获取用户信息成功！\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `获取用户信息失败: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
