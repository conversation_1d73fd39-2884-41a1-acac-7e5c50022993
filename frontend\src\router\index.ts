import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      component: () => import('@/layouts/MainLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'Dashboard',
          component: () => import('@/views/DashboardView.vue'),
          meta: { title: '控制面板' }
        },
        {
          path: '/items',
          name: 'Items',
          component: () => import('@/views/ItemsView.vue'),
          meta: { title: '商品管理' }
        },
        {
          path: '/orders',
          name: 'Orders',
          component: () => import('@/views/OrdersView.vue'),
          meta: { title: '租赁订单' }
        },
        {
          path: '/users',
          name: 'Users',
          component: () => import('@/views/UsersView.vue'),
          meta: { title: '用户管理', requiresAdmin: true }
        },
        {
          path: '/statistics',
          name: 'Statistics',
          component: () => import('@/views/StatisticsView.vue'),
          meta: { title: '数据统计' }
        },
        {
          path: '/settings',
          name: 'Settings',
          component: () => import('@/views/SettingsView.vue'),
          meta: { title: '系统设置', requiresAdmin: true }
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 如果有token但没有用户信息，先尝试初始化用户状态
  if (authStore.token && !authStore.user) {
    try {
      await authStore.initUser()
    } catch (error) {
      // 初始化失败，清除无效token（不显示退出消息）
      authStore.logout(false)
    }
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false && !authStore.isAuthenticated) {
    next('/login')
    return
  }

  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && authStore.user?.role !== 'ADMIN') {
    ElMessage.error('权限不足')
    next('/dashboard')
    return
  }

  // 如果已登录且访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/dashboard')
    return
  }

  next()
})

export default router
