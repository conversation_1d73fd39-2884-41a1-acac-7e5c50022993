{"version": 3, "file": "watermark.js", "sources": ["../../../../../../packages/components/watermark/src/watermark.vue"], "sourcesContent": ["<template>\n  <div ref=\"containerRef\" :style=\"[style]\">\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  onBeforeUnmount,\n  onMounted,\n  ref,\n  shallowRef,\n  watch,\n} from 'vue'\nimport { useMutationObserver } from '@vueuse/core'\nimport { isArray, isUndefined } from '@element-plus/utils'\nimport { watermarkProps } from './watermark'\nimport { getPixelRatio, getStyleStr, reRendering } from './utils'\nimport useClips, { FontGap } from './useClips'\nimport type { WatermarkProps } from './watermark'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElWatermark',\n})\n\nconst style: CSSProperties = {\n  position: 'relative',\n}\n\nconst props = defineProps(watermarkProps)\nconst color = computed(() => props.font?.color ?? 'rgba(0,0,0,.15)')\nconst fontSize = computed(() => props.font?.fontSize ?? 16)\nconst fontWeight = computed(() => props.font?.fontWeight ?? 'normal')\nconst fontStyle = computed(() => props.font?.fontStyle ?? 'normal')\nconst fontFamily = computed(() => props.font?.fontFamily ?? 'sans-serif')\nconst textAlign = computed(() => props.font?.textAlign ?? 'center')\nconst textBaseline = computed(() => props.font?.textBaseline ?? 'hanging')\n\nconst gapX = computed(() => props.gap[0])\nconst gapY = computed(() => props.gap[1])\nconst gapXCenter = computed(() => gapX.value / 2)\nconst gapYCenter = computed(() => gapY.value / 2)\nconst offsetLeft = computed(() => props.offset?.[0] ?? gapXCenter.value)\nconst offsetTop = computed(() => props.offset?.[1] ?? gapYCenter.value)\n\nconst getMarkStyle = () => {\n  const markStyle: CSSProperties = {\n    zIndex: props.zIndex,\n    position: 'absolute',\n    left: 0,\n    top: 0,\n    width: '100%',\n    height: '100%',\n    pointerEvents: 'none',\n    backgroundRepeat: 'repeat',\n  }\n\n  /** Calculate the style of the offset */\n  let positionLeft = offsetLeft.value - gapXCenter.value\n  let positionTop = offsetTop.value - gapYCenter.value\n  if (positionLeft > 0) {\n    markStyle.left = `${positionLeft}px`\n    markStyle.width = `calc(100% - ${positionLeft}px)`\n    positionLeft = 0\n  }\n  if (positionTop > 0) {\n    markStyle.top = `${positionTop}px`\n    markStyle.height = `calc(100% - ${positionTop}px)`\n    positionTop = 0\n  }\n  markStyle.backgroundPosition = `${positionLeft}px ${positionTop}px`\n\n  return markStyle\n}\n\nconst containerRef = shallowRef<HTMLDivElement | null>(null)\nconst watermarkRef = shallowRef<HTMLDivElement>()\nconst stopObservation = ref(false)\n\nconst destroyWatermark = () => {\n  if (watermarkRef.value) {\n    watermarkRef.value.remove()\n    watermarkRef.value = undefined\n  }\n}\nconst appendWatermark = (base64Url: string, markWidth: number) => {\n  if (containerRef.value && watermarkRef.value) {\n    stopObservation.value = true\n    watermarkRef.value.setAttribute(\n      'style',\n      getStyleStr({\n        ...getMarkStyle(),\n        backgroundImage: `url('${base64Url}')`,\n        backgroundSize: `${Math.floor(markWidth)}px`,\n      })\n    )\n    containerRef.value?.append(watermarkRef.value)\n    // Delayed execution\n    setTimeout(() => {\n      stopObservation.value = false\n    })\n  }\n}\n\n/**\n * Get the width and height of the watermark. The default values are as follows\n * Image: [120, 64]; Content: It's calculated by content;\n */\nconst getMarkSize = (ctx: CanvasRenderingContext2D) => {\n  let defaultWidth = 120\n  let defaultHeight = 64\n\n  const { image, content, width, height, rotate } = props\n\n  if (!image && ctx.measureText) {\n    ctx.font = `${Number(fontSize.value)}px ${fontFamily.value}`\n\n    const contents = isArray(content) ? content : [content]\n    let maxWidth = 0\n    let maxHeight = 0\n\n    contents.forEach((item) => {\n      const {\n        width,\n        fontBoundingBoxAscent,\n        fontBoundingBoxDescent,\n        actualBoundingBoxAscent,\n        actualBoundingBoxDescent,\n      } = ctx.measureText(item!)\n      // Using `actualBoundingBoxAscent` to be compatible with lower version browsers (eg: Firefox < 116)\n      const height = isUndefined(fontBoundingBoxAscent)\n        ? actualBoundingBoxAscent + actualBoundingBoxDescent\n        : fontBoundingBoxAscent + fontBoundingBoxDescent\n\n      if (width > maxWidth) maxWidth = Math.ceil(width)\n      if (height > maxHeight) maxHeight = Math.ceil(height)\n    })\n\n    defaultWidth = maxWidth\n    defaultHeight =\n      maxHeight * contents.length + (contents.length - 1) * FontGap\n\n    const angle = (Math.PI / 180) * Number(rotate)\n    const space = Math.ceil(Math.abs(Math.sin(angle) * defaultHeight) / 2)\n\n    defaultWidth += space\n  }\n\n  return [width ?? defaultWidth, height ?? defaultHeight] as const\n}\n\nconst getClips = useClips()\n\nconst renderWatermark = () => {\n  const canvas = document.createElement('canvas')\n  const ctx = canvas.getContext('2d')\n  const image = props.image\n  const content = props.content\n  const rotate = props.rotate\n\n  if (ctx) {\n    if (!watermarkRef.value) {\n      watermarkRef.value = document.createElement('div')\n    }\n\n    const ratio = getPixelRatio()\n    const [markWidth, markHeight] = getMarkSize(ctx)\n\n    const drawCanvas = (\n      drawContent?: NonNullable<WatermarkProps['content']> | HTMLImageElement\n    ) => {\n      const [textClips, clipWidth] = getClips(\n        drawContent || '',\n        rotate,\n        ratio,\n        markWidth,\n        markHeight,\n        {\n          color: color.value,\n          fontSize: fontSize.value,\n          fontStyle: fontStyle.value,\n          fontWeight: fontWeight.value,\n          fontFamily: fontFamily.value,\n          textAlign: textAlign.value,\n          textBaseline: textBaseline.value,\n        },\n        gapX.value,\n        gapY.value\n      )\n\n      appendWatermark(textClips, clipWidth)\n    }\n\n    if (image) {\n      const img = new Image()\n      img.onload = () => {\n        drawCanvas(img)\n      }\n      img.onerror = () => {\n        drawCanvas(content)\n      }\n      img.crossOrigin = 'anonymous'\n      img.referrerPolicy = 'no-referrer'\n      img.src = image\n    } else {\n      drawCanvas(content)\n    }\n  }\n}\n\nonMounted(() => {\n  renderWatermark()\n})\n\nwatch(\n  () => props,\n  () => {\n    renderWatermark()\n  },\n  {\n    deep: true,\n    flush: 'post',\n  }\n)\n\nonBeforeUnmount(() => {\n  destroyWatermark()\n})\n\nconst onMutate = (mutations: MutationRecord[]) => {\n  if (stopObservation.value) {\n    return\n  }\n  mutations.forEach((mutation) => {\n    if (reRendering(mutation, watermarkRef.value)) {\n      destroyWatermark()\n      renderWatermark()\n    }\n  })\n}\n\nuseMutationObserver(containerRef, onMutate, {\n  attributes: true,\n  subtree: true,\n  childList: true,\n})\n</script>\n"], "names": ["computed", "shallowRef", "ref", "width", "getStyleStr", "height", "isArray", "isUndefined", "FontGap", "getPixelRatio", "onMounted", "watch", "onBeforeUnmount"], "mappings": ";;;;;;;;;;;;;uCAuBc,CAAA;AAAA,EACZ,IAAM,EAAA,aAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAEA,IAAA,MAAM,KAAuB,GAAA;AAAA,MAC3B,QAAU,EAAA,UAAA;AAAA,KACZ,CAAA;AAGA,IAAA,MAAM,QAAQA,YAAS,CAAA,MAAM;AAC7B,MAAA,IAAM;AACN,MAAA,mBAA4B,KAAA,CAAA,IAAA,KAAY,IAAA,GAAA,iBAAoB,KAAQ,IAAA,GAAA,EAAA,GAAA,iBAAA,CAAA;AACpE,KAAA,CAAA,CAAA;AACA,IAAA,MAAM,uBAAsB,CAAA,MAAA;AAC5B,MAAA,IAAM;AACN,MAAA,mBAAqB,KAAS,CAAA,IAAA,KAAM,IAAM,GAAA,KAAM,oBAAyB,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AAEzE,KAAA,CAAA,CAAA;AACA,IAAA,MAAM,UAAgB,GAAAA,YAAA,CAAM,MAAM;AAClC,MAAA,IAAM,EAAa,EAAA,EAAA,CAAA;AACnB,MAAA,OAAmB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAe,KAAA,YAAc,CAAA,GAAA,EAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,QAAA,CAAA;AAChD,KAAM,CAAA,CAAA;AACN,IAAM,MAAA,SAAA,GAAYA,aAAS,MAAM;AAEjC,MAAA,IAAM;AACJ,MAAA,OAAiC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,QAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AACjB,IAAA,MACJ,UAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MAAA,IACJ,EAAA,EAAA,EAAA,CAAA;AAAA,MAAA,OACD,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,YAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AACE,IAAA,MACC,SAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MAAA,IACO,EAAA,EAAA,EAAA,CAAA;AAAA,MAAA,OACG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,QAAA,CAAA;AAAA,KACpB,CAAA,CAAA;AAGA,IAAI,MAAA,YAAA,GAAeA,YAAW,CAAA,MAAA;AAC9B,MAAI,IAAA,EAAA,EAAA,EAAA,CAAA;AACJ,MAAA,mBAAmB,KAAG,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,IAAA,GAAA,EAAA,GAAA,SAAA,CAAA;AACpB,KAAU,CAAA,CAAA;AACV,IAAU,MAAA,IAAA,GAAAA,YAAA,CAAQ,eAAe,CAAY,CAAA,CAAA,CAAA,CAAA;AAC7C,IAAe,MAAA,IAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,IACjB,MAAA,UAAA,GAAAA,YAAA,CAAA,MAAA,IAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAI,yBAAiB,CAAA,MAAA,IAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACnB,IAAU,MAAA,UAAA,GAAAA,YAAoB,CAAA,MAAA;AAC9B,MAAU,IAAA,EAAA,EAAA,EAAA,CAAA;AACV,MAAc,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,KAChB,CAAA,CAAA;AACA,IAAA,MAAA,SAA+B,GAAAA,YAAA,CAAA,MAAA;AAE/B,MAAO,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACT,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAEA,KAAM,CAAA,CAAA;AACN,IAAA,MAAM,eAAe,MAA2B;AAChD,MAAM,MAAA,SAAA,GAAA;AAEN,QAAA;AACE,QAAA,oBAAwB;AACtB,QAAA,IAAA,EAAA,CAAA;AACA,QAAA,GAAA,EAAA,CAAA;AAAqB,QACvB,KAAA,EAAA,MAAA;AAAA,QACF,MAAA,EAAA,MAAA;AACA,QAAM,aAAA,EAAA,MAAmB;AACvB,QAAI,gBAAsB,EAAA,QAAA;AACxB,OAAA,CAAA;AACA,MAAA,IAAA,YAAmB,GAAA,UAAA,CAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,MACjB,IAAA,WAAA,GAAA,SAAA,CAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,MAAA,IACA,YAAY,GAAA,CAAA,EAAA;AAAA,QAAA,SACM,CAAA,IAAA,GAAA,CAAA,EAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AAAA,QAChB,SAAA,CAAA,KAAA,GAAA,CAAA,YAAkC,EAAA,YAAA,CAAA,GAAA,CAAA,CAAA;AAAA,QAAA,YAClB,GAAA,CAAA,CAAA;AAAwB,OAAA;AACzC,MACH,IAAA,WAAA,GAAA,CAAA,EAAA;AACA,QAAa,SAAA,CAAA,GAAA,GAAA,CAAA,EAAA,WAAc,CAAA,EAAA,CAAA,CAAA;AAE3B,QAAA,SAAA,CAAA,MAAiB,GAAA,CAAA,YAAA,EAAA,WAAA,CAAA,GAAA,CAAA,CAAA;AACf,QAAA,WAAA,GAAA,CAAA,CAAA;AAAwB,OAAA;AACzB,MACH,SAAA,CAAA,kBAAA,GAAA,CAAA,EAAA,YAAA,CAAA,GAAA,EAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AAAA,MACF,OAAA,SAAA,CAAA;AAMA,KAAM,CAAA;AACJ,IAAA,MAAI,YAAe,GAAAC,cAAA,CAAA,IAAA,CAAA,CAAA;AACnB,IAAA,MAAI,YAAgB,GAAAA,cAAA,EAAA,CAAA;AAEpB,IAAA,MAAA,eAAe,GAAAC,OAAgB,CAAA,KAAA,CAAA,CAAA;AAE/B,IAAI,MAAA,gBAA2B,GAAA,MAAA;AAC7B,MAAI,IAAA,YAAU,CAAO,KAAA,EAAA;AAErB,QAAA,kBAAyB,CAAA,MAAA,EAAA,CAAA;AACzB,QAAA,YAAe,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AACf,OAAA;AAEA,KAAS,CAAA;AACP,IAAM,MAAA,eAAA,GAAA,CAAA,SAAA,EAAA,SAAA,KAAA;AAAA,MAAA,IAAA,EACJ,CAAAC;AAAA,MACA,IAAA,YAAA,CAAA,KAAA,IAAA,YAAA,CAAA,KAAA,EAAA;AAAA,QACA,eAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,QACA,YAAA,CAAA,KAAA,CAAA,YAAA,CAAA,OAAA,EAAAC,iBAAA,CAAA;AAAA,UACA,GAAA,YAAA,EAAA;AAAA,UACF,eAAQ,EAAA,CAAA,KAAiB,EAAA,SAAA,CAAA,EAAA,CAAA;AAEzB,UAAA,gBAAe,CAAY,EAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA;AAI3B,SAAA,CAAA,CAAA,CAAA;AACA,QAAA,CAAA,EAAA,GAAIC,YAAS,CAAA,KAAA,KAAuB,IAAA,GAAA,KAAA,CAAA,YAAgB,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACtD,UAAC,CAAA,MAAA;AAED,UAAe,eAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACf,SAAA,CAAA,CAAA;AAGA,OAAA;AACA,KAAM,CAAA;AAEN,IAAgB,MAAA,WAAA,GAAA,CAAA,GAAA,KAAA;AAAA,MAClB,IAAA,YAAA,GAAA,GAAA,CAAA;AAEA,MAAA,IAAA,aAAiB,GAAc,EAAA,CAAA;AAAuB,MACxD,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,EAAA,MAAA,EAAA,GAAA,KAAA,CAAA;AAEA,MAAA,IAAM,aAAoB,CAAA,WAAA,EAAA;AAE1B,QAAA,oBAAwB,CAAM,QAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,UAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAC5B,QAAM,MAAA,QAAkB,GAAAC,cAAA,CAAA,OAAA,CAAA,GAAc,OAAQ,GAAA,CAAA,OAAA,CAAA,CAAA;AAC9C,QAAM,IAAA,QAAa,GAAA,CAAA,CAAA;AACnB,QAAA,IAAM,SAAc,GAAA,CAAA,CAAA;AACpB,QAAA,gBAAsB,CAAA,CAAA,IAAA,KAAA;AACtB,UAAA;AAEA,YAAS,KAAA,EAAA,MAAA;AACP,YAAI,qBAAqB;AACvB,YAAa,sBAAiB;AAAmB,YACnD,uBAAA;AAEA,YAAA,wBAA4B;AAC5B,WAAA,GAAM,GAAC,CAAA,WAAqB,CAAA,IAAA,CAAA,CAAA;AAE5B,UAAM,MAAA,OAAA,GAAAC,iBAED,CAAA,qBAAA,CAAA,GAAA,uBAAA,GAAA,wBAAA,GAAA,qBAAA,GAAA,sBAAA,CAAA;AACH,UAAM,IAAA,MAAY,GAAA,QAAA;AAAa,YAC7B,QAAe,GAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;AAAA,UACf,IAAA,OAAA,GAAA,SAAA;AAAA,YACA,SAAA,GAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;AAAA,SACA,CAAA,CAAA;AAAA,QACA,YAAA,GAAA,QAAA,CAAA;AAAA,QACA,aAAA,GAAA,SAAA,GAAA,QAAA,CAAA,MAAA,GAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,IAAAC,gBAAA,CAAA;AAAA,QAAA,MACE,QAAa,IAAA,CAAA,EAAA,GAAA,GAAA,GAAA,MAAA,CAAA,MAAA,CAAA,CAAA;AAAA,QAAA,MACb,YAAmB,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA,CAAA,GAAA,aAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,QAAA,qBACE,CAAA;AAAA,OAAA;AACE,MAAA,OAAA,CACvB,aAAuB,GAAA,KAAA,GAAA,YAAA,EAAA,MAAA,IAAA,IAAA,GAAA,MAAA,GAAA,aAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AACF,IAAA,MAAA,8BACM,EAAA,CAAA;AAAA,IAC7B,MAAA,eAAA,GAAA,MAAA;AAAA,MAAA,MACA,MAAK,GAAA,QAAA,CAAA,aAAA,CAAA,QAAA,CAAA,CAAA;AAAA,MAAA,MACL,GAAK,GAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MACP,MAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AAEA,MAAA,MAAA,OAAA,GAAA;AAAoC,MACtC,MAAA,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA;AAEA,MAAA,IAAA,GAAW,EAAA;AACT,QAAM,IAAA,CAAA,aAAU,KAAM,EAAA;AACtB,UAAA,aAAa,KAAM,GAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AACjB,SAAA;AAAc,QAChB,MAAA,KAAA,GAAAC,mBAAA,EAAA,CAAA;AACA,QAAA,MAAI,UAAU,EAAM,UAAA,CAAA,GAAA,WAAA,CAAA,GAAA,CAAA,CAAA;AAClB,QAAA,MAAA,UAAkB,GAAA,CAAA,WAAA,KAAA;AAAA,UACpB,MAAA,CAAA,SAAA,EAAA,SAAA,CAAA,GAAA,QAAA,CAAA,WAAA,IAAA,EAAA,EAAA,MAAA,EAAA,KAAA,EAAA,SAAA,EAAA,UAAA,EAAA;AACA,YAAA,KAAkB,EAAA,KAAA,CAAA,KAAA;AAClB,YAAA,QAAqB,EAAA,QAAA,CAAA,KAAA;AACrB,YAAA,SAAU,EAAA,SAAA,CAAA,KAAA;AAAA,YACL,UAAA,EAAA,UAAA,CAAA,KAAA;AACL,YAAA,UAAkB,EAAA,UAAA,CAAA,KAAA;AAAA,YACpB,SAAA,EAAA,SAAA,CAAA,KAAA;AAAA,YACF,YAAA,EAAA,YAAA,CAAA,KAAA;AAAA,WACF,EAAA,IAAA,CAAA,KAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAEA,UAAA,eAAgB,CAAA,SAAA,EAAA,SAAA,CAAA,CAAA;AACd,SAAgB,CAAA;AAAA,QACjB,IAAA,KAAA,EAAA;AAED,UAAA,MAAA,GAAA,GAAA,IAAA,KAAA,EAAA,CAAA;AAAA,UACQ,GAAA,CAAA,MAAA,GAAA,MAAA;AAAA,YACA,UAAA,CAAA,GAAA,CAAA,CAAA;AACJ,WAAgB,CAAA;AAAA,UAClB,GAAA,CAAA,OAAA,GAAA,MAAA;AAAA,YACA,UAAA,CAAA,OAAA,CAAA,CAAA;AAAA,WACQ,CAAA;AAAA,UACC,GAAA,CAAA,WAAA,GAAA,WAAA,CAAA;AAAA,UACT,GAAA,CAAA,cAAA,GAAA,aAAA,CAAA;AAAA,UACF,GAAA,CAAA,GAAA,GAAA,KAAA,CAAA;AAEA,SAAA,MAAA;AACE,UAAiB,UAAA,CAAA,OAAA,CAAA,CAAA;AAAA,SAClB;AAED,OAAM;AACJ,KAAA,CAAA;AACE,IAAAC,aAAA,CAAA,MAAA;AAAA,MACF,eAAA,EAAA,CAAA;AACA,KAAU,CAAA,CAAA;AACR,IAAAC,SAAA,CAAA,MAAgB,KAAA,EAAA,MAAA;AACd,MAAiB,eAAA,EAAA,CAAA;AACjB,KAAgB,EAAA;AAAA,MAClB,IAAA,EAAA,IAAA;AAAA,MACF,KAAC,EAAA,MAAA;AAAA,KACH,CAAA,CAAA;AAEA,IAAAC,mBAAA,CAAA;AAA4C,MAC1C,gBAAY,EAAA,CAAA;AAAA,KAAA,CACZ,CAAS;AAAA,IAAA,MACE,QAAA,GAAA,CAAA,SAAA,KAAA;AAAA,MACZ,IAAA,eAAA,CAAA,KAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}