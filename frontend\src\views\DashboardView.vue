<template>
  <div class="dashboard">
    <div class="page-header">
      <h1>控制面板</h1>
      <p>欢迎回来，{{ authStore.user?.username }}！</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card primary-card">
          <div class="stats-content">
            <div class="stats-icon primary">
              <el-icon :size="28"><Box /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalItems }}</div>
              <div class="stats-label">商品总数</div>
              <div class="stats-trend">
                <el-icon class="trend-icon"><TrendCharts /></el-icon>
                <span class="trend-text">+12%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card success-card">
          <div class="stats-content">
            <div class="stats-icon success">
              <el-icon :size="28"><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.availableItems }}</div>
              <div class="stats-label">可租商品</div>
              <div class="stats-trend">
                <el-icon class="trend-icon"><TrendCharts /></el-icon>
                <span class="trend-text">+8%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card warning-card">
          <div class="stats-content">
            <div class="stats-icon warning">
              <el-icon :size="28"><ShoppingCart /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.rentedItems }}</div>
              <div class="stats-label">租赁中</div>
              <div class="stats-trend">
                <el-icon class="trend-icon"><TrendCharts /></el-icon>
                <span class="trend-text">+5%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card danger-card">
          <div class="stats-content">
            <div class="stats-icon danger">
              <el-icon :size="28"><Tools /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.maintenanceItems }}</div>
              <div class="stats-label">维护中</div>
              <div class="stats-trend">
                <el-icon class="trend-icon"><TrendCharts /></el-icon>
                <span class="trend-text">-2%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表和数据 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>租赁趋势</span>
              <el-button type="text" size="small">查看详情</el-button>
            </div>
          </template>
          <div class="chart-container">
            <v-chart 
              class="chart" 
              :option="lineChartOption" 
              autoresize
            />
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>商品分类分布</span>
              <el-button type="text" size="small">查看详情</el-button>
            </div>
          </template>
          <div class="chart-container">
            <v-chart 
              class="chart" 
              :option="pieChartOption" 
              autoresize
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近订单 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="recent-orders">
          <template #header>
            <div class="card-header">
              <span>最近订单</span>
              <el-button type="primary" size="small" @click="$router.push('/orders')">
                查看全部
              </el-button>
            </div>
          </template>
          
          <el-table :data="recentOrders" style="width: 100%">
            <el-table-column prop="id" label="订单号" width="100" />
            <el-table-column prop="itemName" label="商品名称" />
            <el-table-column prop="userName" label="用户" width="120" />
            <el-table-column prop="startDate" label="开始日期" width="120" />
            <el-table-column prop="endDate" label="结束日期" width="120" />
            <el-table-column prop="totalPrice" label="总价" width="100">
              <template #default="{ row }">
                ¥{{ row.totalPrice.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag 
                  :type="getStatusType(row.status)"
                  size="small"
                >
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import {
  Box,
  CircleCheck,
  ShoppingCart,
  Tools,
  TrendCharts
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth.ts'
import type { DashboardStats } from '@/types'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const authStore = useAuthStore()

// 统计数据
const stats = ref<DashboardStats>({
  totalItems: 42,
  availableItems: 32,
  rentedItems: 8,
  maintenanceItems: 2,
  totalOrders: 156,
  activeOrders: 24,
  totalUsers: 89,
  todayOrders: 8
})

// 最近订单
const recentOrders = ref([
  {
    id: 1001,
    itemName: 'MacBook Pro 16"',
    userName: '张三',
    startDate: '2024-01-15',
    endDate: '2024-01-20',
    totalPrice: 449.95,
    status: 'ACTIVE'
  },
  {
    id: 1002,
    itemName: 'Canon EOS R5',
    userName: '李四',
    startDate: '2024-01-14',
    endDate: '2024-01-18',
    totalPrice: 278.00,
    status: 'COMPLETED'
  },
  {
    id: 1003,
    itemName: 'DJI Mavic 3 Pro',
    userName: '王五',
    startDate: '2024-01-16',
    endDate: '2024-01-21',
    totalPrice: 600.00,
    status: 'PENDING'
  },
  {
    id: 1004,
    itemName: 'Camping Tent',
    userName: '赵六',
    startDate: '2024-01-13',
    endDate: '2024-01-15',
    totalPrice: 71.50,
    status: 'CANCELLED'
  }
])

// 折线图配置
const lineChartOption = ref({
  title: {
    text: '最近7天租赁趋势',
    left: 'center',
    textStyle: {
      fontSize: 14,
      color: '#333'
    }
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['1/10', '1/11', '1/12', '1/13', '1/14', '1/15', '1/16']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '新增订单',
      type: 'line',
      data: [5, 8, 12, 6, 9, 15, 8],
      smooth: true,
      itemStyle: {
        color: '#4361ee'
      }
    }
  ]
})

// 饼图配置
const pieChartOption = ref({
  title: {
    text: '商品分类分布',
    left: 'center',
    textStyle: {
      fontSize: 14,
      color: '#333'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    bottom: '5%',
    left: 'center'
  },
  series: [
    {
      name: '商品分类',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      data: [
        { value: 12, name: '电子产品' },
        { value: 8, name: '摄影器材' },
        { value: 6, name: '无人机' },
        { value: 10, name: '工具' },
        { value: 6, name: '户外装备' }
      ],
      itemStyle: {
        borderRadius: 5,
        borderColor: '#fff',
        borderWidth: 2
      }
    }
  ]
})

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: '',
    ACTIVE: 'success',
    COMPLETED: 'info',
    CANCELLED: 'danger'
  }
  return statusMap[status] || ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: '待处理',
    ACTIVE: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

// 加载数据
const loadDashboardData = async () => {
  try {
    // TODO: 调用API获取仪表板数据
    // const data = await dashboardApi.getStats()
    // stats.value = data

    // 临时使用模拟数据
    console.log('仪表板数据加载完成')
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .page-header {
    margin-bottom: 24px;

    h1 {
      margin: 0 0 8px;
      color: var(--dark-color);
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 1rem;
    }
  }

  .stats-row {
    margin-bottom: 24px;
  }

  .stats-card {
    border: none;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    position: relative;

    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--box-shadow-lg);
    }

    &.primary-card {
      background: var(--gradient-primary);
      color: white;

      .stats-number, .stats-label {
        color: white;
      }
    }

    &.success-card {
      background: var(--gradient-success);
      color: white;

      .stats-number, .stats-label {
        color: white;
      }
    }

    &.warning-card {
      background: var(--gradient-warning);
      color: white;

      .stats-number, .stats-label {
        color: white;
      }
    }

    &.danger-card {
      background: var(--gradient-secondary);
      color: white;

      .stats-number, .stats-label {
        color: white;
      }
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 100px;
      height: 100px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      transform: translate(30px, -30px);
    }

    :deep(.el-card__body) {
      padding: 24px;
      position: relative;
      z-index: 1;
    }
  }

  .stats-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .stats-icon {
      width: 56px;
      height: 56px;
      border-radius: var(--border-radius);
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      color: white;
      margin-right: 16px;
      flex-shrink: 0;
    }

    .stats-info {
      flex: 1;
      min-width: 0;

      .stats-number {
        font-size: 2.2rem;
        font-weight: 800;
        line-height: 1;
        margin-bottom: 6px;
        background: linear-gradient(45deg, rgba(255,255,255,1), rgba(255,255,255,0.8));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .stats-label {
        font-size: 0.95rem;
        opacity: 0.9;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .stats-trend {
        display: flex;
        align-items: center;
        font-size: 0.85rem;
        opacity: 0.8;

        .trend-icon {
          margin-right: 4px;
          font-size: 14px;
        }

        .trend-text {
          font-weight: 600;
        }
      }
    }
  }

  .charts-row {
    margin-bottom: 24px;
  }

  .chart-card,
  .recent-orders {
    border: none;
    box-shadow: var(--box-shadow);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: var(--dark-color);
    }

    .chart-container {
      height: 300px;

      .chart {
        height: 100%;
        width: 100%;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    .stats-content {
      .stats-icon {
        width: 50px;
        height: 50px;
        margin-right: 12px;

        .el-icon {
          font-size: 24px;
        }
      }

      .stats-info {
        .stats-number {
          font-size: 1.5rem;
        }

        .stats-label {
          font-size: 0.8rem;
        }
      }
    }

    .chart-container {
      height: 250px;
    }
  }
}

@media (max-width: 480px) {
  .dashboard {
    .page-header {
      h1 {
        font-size: 1.5rem;
      }

      p {
        font-size: 0.9rem;
      }
    }

    .stats-content {
      flex-direction: column;
      text-align: center;

      .stats-icon {
        margin-right: 0;
        margin-bottom: 12px;
      }
    }
  }
}
</style>
