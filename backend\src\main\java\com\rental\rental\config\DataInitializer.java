package com.rental.rental.config;

import com.rental.rental.model.User;
import com.rental.rental.model.Item;
import com.rental.rental.repository.UserRepository;
import com.rental.rental.repository.ItemRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {

    private final UserRepository userRepository;
    private final ItemRepository itemRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        // 初始化管理员用户
        if (!userRepository.existsByUsername("admin")) {
            User admin = new User();
            admin.setUsername("admin");
            admin.setEmail("<EMAIL>");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setRole(User.Role.ADMIN);
            userRepository.save(admin);
        }

        // 初始化测试用户
        if (!userRepository.existsByUsername("user")) {
            User user = new User();
            user.setUsername("user");
            user.setEmail("<EMAIL>");
            user.setPassword(passwordEncoder.encode("user123"));
            user.setRole(User.Role.USER);
            userRepository.save(user);
        }

        // 初始化测试商品
        if (itemRepository.count() == 0) {
            Item item1 = new Item();
            item1.setName("MacBook Pro 16\"");
            item1.setDescription("2023款 M2 Max芯片 32GB内存");
            item1.setDailyPrice(new BigDecimal("89.99"));
            item1.setStock(5);
            item1.setCategory("Electronics");
            item1.setStatus(Item.Status.AVAILABLE);
            item1.setImageUrl("https://images.unsplash.com/photo-1517336714731-489689fd1ca8?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80");
            itemRepository.save(item1);

            Item item2 = new Item();
            item2.setName("Canon EOS R5");
            item2.setDescription("全画幅专业微单相机");
            item2.setDailyPrice(new BigDecimal("69.50"));
            item2.setStock(3);
            item2.setCategory("Photography");
            item2.setStatus(Item.Status.AVAILABLE);
            item2.setImageUrl("https://images.unsplash.com/photo-1516035069371-29a1b244cc32?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80");
            itemRepository.save(item2);

            Item item3 = new Item();
            item3.setName("DJI Mavic 3 Pro");
            item3.setDescription("专业航拍无人机");
            item3.setDailyPrice(new BigDecimal("120.00"));
            item3.setStock(2);
            item3.setCategory("Drone");
            item3.setStatus(Item.Status.AVAILABLE);
            item3.setImageUrl("https://images.unsplash.com/photo-1579820010410-c10411aaaa88?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80");
            itemRepository.save(item3);

            Item item4 = new Item();
            item4.setName("Bosch Drill Set");
            item4.setDescription("专业电动工具套装");
            item4.setDailyPrice(new BigDecimal("25.00"));
            item4.setStock(10);
            item4.setCategory("Tools");
            item4.setStatus(Item.Status.AVAILABLE);
            item4.setImageUrl("https://images.unsplash.com/photo-1585155770447-2f66e2a397b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80");
            itemRepository.save(item4);

            Item item5 = new Item();
            item5.setName("Camping Tent");
            item5.setDescription("4人专业露营帐篷");
            item5.setDailyPrice(new BigDecimal("35.75"));
            item5.setStock(8);
            item5.setCategory("Outdoor");
            item5.setStatus(Item.Status.AVAILABLE);
            item5.setImageUrl("https://images.unsplash.com/photo-1504280390367-361c6d9f38f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80");
            itemRepository.save(item5);
        }
    }
}
