import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

export interface SystemSettings {
  systemName: string
  systemDescription: string
  defaultRentalDays: number
  maxRentalDays: number
  autoConfirmOrder: boolean
  allowUserRegistration: boolean
  emailNotification: boolean
}

export const useSettingsStore = defineStore('settings', () => {
  // 系统设置
  const settings = reactive<SystemSettings>({
    systemName: '商品租赁系统',
    systemDescription: '专业、便捷的商品租赁管理平台',
    defaultRentalDays: 7,
    maxRentalDays: 30,
    autoConfirmOrder: false,
    allowUserRegistration: true,
    emailNotification: true
  })

  const loading = ref(false)

  // 保存设置
  const saveSettings = async (newSettings: Partial<SystemSettings>) => {
    loading.value = true
    try {
      // 更新设置
      Object.assign(settings, newSettings)
      
      // 保存到localStorage
      localStorage.setItem('systemSettings', JSON.stringify(settings))
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      ElMessage.success('设置保存成功')
      return true
    } catch (error) {
      ElMessage.error('保存设置失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 重置设置
  const resetSettings = () => {
    Object.assign(settings, {
      systemName: '商品租赁系统',
      systemDescription: '专业、便捷的商品租赁管理平台',
      defaultRentalDays: 7,
      maxRentalDays: 30,
      autoConfirmOrder: false,
      allowUserRegistration: true,
      emailNotification: true
    })
    localStorage.removeItem('systemSettings')
    ElMessage.success('设置已重置')
  }

  // 从localStorage加载设置
  const loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem('systemSettings')
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings)
        Object.assign(settings, parsed)
      }
    } catch (error) {
      console.error('加载系统设置失败:', error)
    }
  }

  // 导出设置
  const exportSettings = () => {
    try {
      const dataStr = JSON.stringify(settings, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `system-settings-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      URL.revokeObjectURL(url)
      ElMessage.success('设置导出成功')
    } catch (error) {
      ElMessage.error('导出设置失败')
    }
  }

  // 导入设置
  const importSettings = (file: File) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string)
          Object.assign(settings, importedSettings)
          localStorage.setItem('systemSettings', JSON.stringify(settings))
          ElMessage.success('设置导入成功')
          resolve(true)
        } catch (error) {
          ElMessage.error('导入设置失败：文件格式错误')
          reject(error)
        }
      }
      reader.onerror = () => {
        ElMessage.error('读取文件失败')
        reject(new Error('读取文件失败'))
      }
      reader.readAsText(file)
    })
  }

  return {
    settings,
    loading,
    saveSettings,
    resetSettings,
    loadSettings,
    exportSettings,
    importSettings
  }
})
