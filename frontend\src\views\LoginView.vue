<template>
  <div class="auth-container">
    <el-card class="auth-card">
      <div class="auth-content">
        <!-- 左侧介绍 -->
        <div class="auth-left">
          <div class="auth-header">
            <el-icon class="auth-logo" :size="60">
              <ShoppingBag />
            </el-icon>
            <h1>商品租赁系统</h1>
            <p>专业、便捷的商品租赁管理平台</p>
          </div>
          
          <div class="auth-features">
            <div class="feature-item">
              <el-icon :size="24"><Box /></el-icon>
              <div>
                <h4>商品管理</h4>
                <p>轻松管理各类租赁商品，跟踪库存状态</p>
              </div>
            </div>
            <div class="feature-item">
              <el-icon :size="24"><ShoppingCart /></el-icon>
              <div>
                <h4>租赁管理</h4>
                <p>管理租赁订单，跟踪租赁状态</p>
              </div>
            </div>
            <div class="feature-item">
              <el-icon :size="24"><TrendCharts /></el-icon>
              <div>
                <h4>数据统计</h4>
                <p>实时分析租赁数据，优化业务决策</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧表单 -->
        <div class="auth-right">
          <!-- 登录表单 -->
          <div v-if="!isRegister" class="form-container">
            <h2>欢迎回来</h2>
            <el-form
              ref="loginFormRef"
              :model="loginForm"
              :rules="loginRules"
              size="large"
              @submit.prevent="handleLogin"
            >
              <el-form-item prop="username">
                <el-input
                  v-model="loginForm.username"
                  placeholder="用户名"
                  :prefix-icon="User"
                />
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  v-model="loginForm.password"
                  type="password"
                  placeholder="密码"
                  :prefix-icon="Lock"
                  show-password
                  @keyup.enter="handleLogin"
                />
              </el-form-item>
              <el-form-item>
                <div class="form-options">
                  <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
                  <el-link type="primary">忘记密码?</el-link>
                </div>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  size="large"
                  style="width: 100%"
                  :loading="authStore.loading"
                  @click="handleLogin"
                >
                  登录
                </el-button>
              </el-form-item>
            </el-form>
            <div class="form-switch">
              <span>还没有账号? </span>
              <el-link type="primary" @click="isRegister = true">立即注册</el-link>
            </div>
          </div>
          
          <!-- 注册表单 -->
          <div v-else class="form-container">
            <h2>创建新账号</h2>
            <el-form
              ref="registerFormRef"
              :model="registerForm"
              :rules="registerRules"
              size="large"
              @submit.prevent="handleRegister"
            >
              <el-form-item prop="username">
                <el-input
                  v-model="registerForm.username"
                  placeholder="用户名"
                  :prefix-icon="User"
                />
              </el-form-item>
              <el-form-item prop="email">
                <el-input
                  v-model="registerForm.email"
                  placeholder="邮箱"
                  :prefix-icon="Message"
                />
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  v-model="registerForm.password"
                  type="password"
                  placeholder="密码"
                  :prefix-icon="Lock"
                  show-password
                />
              </el-form-item>
              <el-form-item prop="confirmPassword">
                <el-input
                  v-model="registerForm.confirmPassword"
                  type="password"
                  placeholder="确认密码"
                  :prefix-icon="Lock"
                  show-password
                  @keyup.enter="handleRegister"
                />
              </el-form-item>
              <el-form-item prop="agreeTerms">
                <el-checkbox v-model="registerForm.agreeTerms">
                  我同意<el-link type="primary">服务条款</el-link>
                </el-checkbox>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  size="large"
                  style="width: 100%"
                  :loading="authStore.loading"
                  @click="handleRegister"
                >
                  注册
                </el-button>
              </el-form-item>
            </el-form>
            <div class="form-switch">
              <span>已有账号? </span>
              <el-link type="primary" @click="isRegister = false">立即登录</el-link>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { User, Lock, Message, ShoppingBag, Box, ShoppingCart, TrendCharts } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth.ts'
import type { LoginForm, RegisterForm } from '@/types'

const router = useRouter()
const authStore = useAuthStore()

// 表单切换
const isRegister = ref(false)

// 表单引用
const loginFormRef = ref<FormInstance>()
const registerFormRef = ref<FormInstance>()

// 登录表单
const loginForm = reactive<LoginForm>({
  username: '',
  password: '',
  rememberMe: false
})

// 注册表单
const registerForm = reactive<RegisterForm>({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 登录表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ]
}

// 注册表单验证规则
const registerRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  agreeTerms: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请同意服务条款'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      const success = await authStore.login(loginForm)
      if (success) {
        router.push('/dashboard')
      }
    }
  })
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  await registerFormRef.value.validate(async (valid) => {
    if (valid) {
      const success = await authStore.register(registerForm)
      if (success) {
        router.push('/dashboard')
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fb, #e6e9ff);
  padding: 20px;
}

.auth-card {
  width: 100%;
  max-width: 1000px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.auth-content {
  display: flex;
  min-height: 600px;
}

.auth-left {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  padding: 40px;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;

  .auth-logo {
    margin-bottom: 20px;
  }

  h1 {
    font-size: 2.5rem;
    margin-bottom: 16px;
    font-weight: 700;
  }

  p {
    font-size: 1.1rem;
    opacity: 0.9;
  }
}

.auth-features {
  .feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24px;

    .el-icon {
      margin-right: 16px;
      margin-top: 4px;
      flex-shrink: 0;
    }

    h4 {
      font-size: 1.2rem;
      margin-bottom: 8px;
      font-weight: 600;
    }

    p {
      font-size: 0.95rem;
      opacity: 0.9;
      line-height: 1.5;
    }
  }
}

.auth-right {
  flex: 1;
  background-color: white;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-container {
  width: 100%;
  max-width: 400px;

  h2 {
    text-align: center;
    margin-bottom: 32px;
    color: var(--dark-color);
    font-weight: 600;
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.form-switch {
  text-align: center;
  margin-top: 20px;
  color: #666;
}

// 响应式设计
@media (max-width: 768px) {
  .auth-content {
    flex-direction: column;
  }

  .auth-left {
    padding: 30px 20px;

    .auth-header h1 {
      font-size: 2rem;
    }

    .auth-features {
      display: none;
    }
  }

  .auth-right {
    padding: 30px 20px;
  }
}
</style>
