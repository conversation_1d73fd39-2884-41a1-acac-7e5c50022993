<template>
  <div class="users-view">
    <div class="page-header">
      <div class="header-left">
        <h1>用户管理</h1>
        <p>管理系统用户</p>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="角色">
          <el-select
            v-model="searchForm.role"
            placeholder="请选择角色"
            clearable
            style="width: 120px"
          >
            <el-option label="管理员" value="ADMIN" />
            <el-option label="用户" value="USER" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table
        :data="users"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="row.role === 'ADMIN' ? 'danger' : 'primary'" size="small">
              {{ row.role === 'ADMIN' ? '管理员' : '用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="注册时间" width="180" />
        <el-table-column prop="orderCount" label="订单数量" width="100" />
        <el-table-column prop="totalSpent" label="总消费" width="120">
          <template #default="{ row }">
            ¥{{ row.totalSpent.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="primary"
              :icon="View"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              v-if="row.role !== 'ADMIN'"
              size="small"
              type="warning"
              @click="handleToggleRole(row)"
            >
              设为管理员
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="用户详情"
      width="600px"
    >
      <div v-if="selectedUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ selectedUser.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ selectedUser.username }}</el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag :type="selectedUser.role === 'ADMIN' ? 'danger' : 'primary'">
              {{ selectedUser.role === 'ADMIN' ? '管理员' : '用户' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ selectedUser.createdAt }}</el-descriptions-item>
          <el-descriptions-item label="订单数量">{{ selectedUser.orderCount }}</el-descriptions-item>
          <el-descriptions-item label="总消费">¥{{ selectedUser.totalSpent.toFixed(2) }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 用户订单历史 -->
        <div class="user-orders" style="margin-top: 20px;">
          <h4>订单历史</h4>
          <el-table :data="selectedUser.orders" size="small">
            <el-table-column prop="id" label="订单号" width="100" />
            <el-table-column prop="itemName" label="商品名称" />
            <el-table-column prop="startDate" label="开始日期" width="120" />
            <el-table-column prop="endDate" label="结束日期" width="120" />
            <el-table-column prop="totalPrice" label="总价" width="100">
              <template #default="{ row }">
                ¥{{ row.totalPrice.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag size="small" :type="getOrderStatusType(row.status)">
                  {{ getOrderStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { View } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const showDetailDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const selectedUser = ref(null)

// 搜索表单
const searchForm = reactive({
  username: '',
  role: ''
})

// 用户列表
const users = ref([
  {
    id: 1,
    username: 'admin',
    role: 'ADMIN',
    createdAt: '2024-01-01 10:00:00',
    orderCount: 0,
    totalSpent: 0,
    orders: []
  },
  {
    id: 2,
    username: '张三',
    role: 'USER',
    createdAt: '2024-01-05 14:30:00',
    orderCount: 3,
    totalSpent: 1327.95,
    orders: [
      {
        id: 1001,
        itemName: 'MacBook Pro 16"',
        startDate: '2024-01-15',
        endDate: '2024-01-20',
        totalPrice: 449.95,
        status: 'ACTIVE'
      },
      {
        id: 1005,
        itemName: 'Canon EOS R5',
        startDate: '2024-01-10',
        endDate: '2024-01-12',
        totalPrice: 139.00,
        status: 'COMPLETED'
      }
    ]
  },
  {
    id: 3,
    username: '李四',
    role: 'USER',
    createdAt: '2024-01-08 09:15:00',
    orderCount: 2,
    totalSpent: 878.00,
    orders: [
      {
        id: 1002,
        itemName: 'Canon EOS R5',
        startDate: '2024-01-14',
        endDate: '2024-01-18',
        totalPrice: 278.00,
        status: 'COMPLETED'
      }
    ]
  }
])

// 获取订单状态类型
const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: '',
    ACTIVE: 'success',
    COMPLETED: 'info',
    CANCELLED: 'danger'
  }
  return statusMap[status] || ''
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: '待处理',
    ACTIVE: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadUsers()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    role: ''
  })
  handleSearch()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadUsers()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadUsers()
}

// 查看用户详情
const handleView = (user: any) => {
  selectedUser.value = user
  showDetailDialog.value = true
}

// 切换用户角色
const handleToggleRole = (user: any) => {
  ElMessageBox.confirm(
    `确定要将用户"${user.username}"设为管理员吗？`,
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // TODO: 调用API更新用户角色
    user.role = 'ADMIN'
    ElMessage.success('用户角色已更新')
  })
}

// 加载用户列表
const loadUsers = () => {
  loading.value = true
  // TODO: 调用API加载用户
  setTimeout(() => {
    total.value = users.value.length
    loading.value = false
  }, 500)
}

onMounted(() => {
  loadUsers()
})
</script>

<style lang="scss" scoped>
.users-view {
  .page-header {
    margin-bottom: 24px;
    
    .header-left {
      h1 {
        margin: 0 0 8px;
        color: var(--dark-color);
        font-weight: 600;
      }
      
      p {
        margin: 0;
        color: #666;
      }
    }
  }
  
  .search-card,
  .table-card {
    margin-bottom: 24px;
    border: none;
    box-shadow: var(--box-shadow);
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .user-detail {
    .user-orders {
      h4 {
        margin-bottom: 16px;
        color: var(--dark-color);
      }
    }
  }
}
</style>
