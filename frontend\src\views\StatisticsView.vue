<template>
  <div class="statistics-view">
    <div class="page-header">
      <h1>数据统计</h1>
      <p>查看系统运营数据和分析报告</p>
    </div>

    <!-- 时间范围选择 -->
    <el-card class="filter-card">
      <el-form inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="refreshData">刷新数据</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="overview-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon revenue">
              <el-icon :size="32"><Money /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">¥{{ stats.totalRevenue.toFixed(2) }}</div>
              <div class="overview-label">总收入</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon orders">
              <el-icon :size="32"><Document /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">{{ stats.totalOrders }}</div>
              <div class="overview-label">总订单</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon users">
              <el-icon :size="32"><UserFilled /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">{{ stats.totalUsers }}</div>
              <div class="overview-label">总用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon rate">
              <el-icon :size="32"><TrendCharts /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">{{ stats.utilizationRate }}%</div>
              <div class="overview-label">利用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 收入趋势 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>收入趋势</span>
              <el-radio-group v-model="revenueChartType" size="small">
                <el-radio-button label="day">日</el-radio-button>
                <el-radio-button label="week">周</el-radio-button>
                <el-radio-button label="month">月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <v-chart 
              class="chart" 
              :option="revenueChartOption" 
              autoresize
            />
          </div>
        </el-card>
      </el-col>
      
      <!-- 订单状态分布 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <span>订单状态分布</span>
          </template>
          <div class="chart-container">
            <v-chart 
              class="chart" 
              :option="orderStatusChartOption" 
              autoresize
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <!-- 热门商品 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <span>热门商品排行</span>
          </template>
          <div class="chart-container">
            <v-chart 
              class="chart" 
              :option="popularItemsChartOption" 
              autoresize
            />
          </div>
        </el-card>
      </el-col>
      
      <!-- 用户活跃度 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <span>用户活跃度</span>
          </template>
          <div class="chart-container">
            <v-chart 
              class="chart" 
              :option="userActivityChartOption" 
              autoresize
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card">
      <template #header>
        <span>详细数据</span>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="商品统计" name="items">
          <el-table :data="itemStats" style="width: 100%">
            <el-table-column prop="name" label="商品名称" />
            <el-table-column prop="category" label="分类" width="120" />
            <el-table-column prop="rentCount" label="租赁次数" width="120" />
            <el-table-column prop="revenue" label="收入" width="120">
              <template #default="{ row }">
                ¥{{ row.revenue.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="utilizationRate" label="利用率" width="120">
              <template #default="{ row }">
                {{ row.utilizationRate }}%
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="用户统计" name="users">
          <el-table :data="userStats" style="width: 100%">
            <el-table-column prop="username" label="用户名" />
            <el-table-column prop="orderCount" label="订单数量" width="120" />
            <el-table-column prop="totalSpent" label="总消费" width="120">
              <template #default="{ row }">
                ¥{{ row.totalSpent.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="lastOrderDate" label="最后订单日期" width="150" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { Money, Document, UserFilled, TrendCharts } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const dateRange = ref([
  dayjs().subtract(30, 'day').toDate(),
  dayjs().toDate()
])
const revenueChartType = ref('day')
const activeTab = ref('items')

// 统计数据
const stats = reactive({
  totalRevenue: 15680.50,
  totalOrders: 156,
  totalUsers: 89,
  utilizationRate: 78.5
})

// 商品统计数据
const itemStats = ref([
  {
    name: 'MacBook Pro 16"',
    category: '电子产品',
    rentCount: 25,
    revenue: 2249.75,
    utilizationRate: 85.2
  },
  {
    name: 'Canon EOS R5',
    category: '摄影器材',
    rentCount: 18,
    revenue: 1251.00,
    utilizationRate: 72.4
  },
  {
    name: 'DJI Mavic 3 Pro',
    category: '无人机',
    rentCount: 15,
    revenue: 1800.00,
    utilizationRate: 68.9
  }
])

// 用户统计数据
const userStats = ref([
  {
    username: '张三',
    orderCount: 8,
    totalSpent: 1327.95,
    lastOrderDate: '2024-01-15'
  },
  {
    username: '李四',
    orderCount: 5,
    totalSpent: 878.00,
    lastOrderDate: '2024-01-12'
  },
  {
    username: '王五',
    orderCount: 3,
    totalSpent: 600.00,
    lastOrderDate: '2024-01-10'
  }
])

// 收入趋势图配置
const revenueChartOption = ref({
  title: {
    text: '收入趋势',
    left: 'center',
    textStyle: {
      fontSize: 14,
      color: '#333'
    }
  },
  tooltip: {
    trigger: 'axis',
    formatter: '{b}: ¥{c}'
  },
  xAxis: {
    type: 'category',
    data: ['1/10', '1/11', '1/12', '1/13', '1/14', '1/15', '1/16']
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '¥{value}'
    }
  },
  series: [
    {
      name: '收入',
      type: 'line',
      data: [580, 720, 650, 890, 1200, 950, 1100],
      smooth: true,
      itemStyle: {
        color: '#4361ee'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(67, 97, 238, 0.3)' },
            { offset: 1, color: 'rgba(67, 97, 238, 0.05)' }
          ]
        }
      }
    }
  ]
})

// 订单状态分布图配置
const orderStatusChartOption = ref({
  title: {
    text: '订单状态分布',
    left: 'center',
    textStyle: {
      fontSize: 14,
      color: '#333'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    bottom: '5%',
    left: 'center'
  },
  series: [
    {
      name: '订单状态',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      data: [
        { value: 24, name: '进行中' },
        { value: 98, name: '已完成' },
        { value: 18, name: '待处理' },
        { value: 16, name: '已取消' }
      ],
      itemStyle: {
        borderRadius: 5,
        borderColor: '#fff',
        borderWidth: 2
      }
    }
  ]
})

// 热门商品排行图配置
const popularItemsChartOption = ref({
  title: {
    text: '热门商品排行',
    left: 'center',
    textStyle: {
      fontSize: 14,
      color: '#333'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'category',
    data: ['Camping Tent', 'Bosch Drill', 'DJI Mavic 3', 'Canon EOS R5', 'MacBook Pro']
  },
  series: [
    {
      name: '租赁次数',
      type: 'bar',
      data: [8, 12, 15, 18, 25],
      itemStyle: {
        color: '#2ec4b6'
      }
    }
  ]
})

// 用户活跃度图配置
const userActivityChartOption = ref({
  title: {
    text: '用户活跃度',
    left: 'center',
    textStyle: {
      fontSize: 14,
      color: '#333'
    }
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '活跃用户',
      type: 'bar',
      data: [12, 15, 18, 22, 25, 28, 20],
      itemStyle: {
        color: '#ff9f1c'
      }
    }
  ]
})

// 处理日期范围变化
const handleDateChange = () => {
  refreshData()
}

// 刷新数据
const refreshData = () => {
  // TODO: 根据日期范围调用API获取数据
  console.log('刷新数据', dateRange.value)
}

// 加载统计数据
const loadStatistics = () => {
  // TODO: 调用API加载统计数据
  console.log('加载统计数据')
}

onMounted(() => {
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.statistics-view {
  .page-header {
    margin-bottom: 24px;

    h1 {
      margin: 0 0 8px;
      color: var(--dark-color);
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #666;
    }
  }

  .filter-card {
    margin-bottom: 24px;
    border: none;
    box-shadow: var(--box-shadow);
  }

  .overview-row {
    margin-bottom: 24px;

    .overview-card {
      border: none;
      box-shadow: var(--box-shadow);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      }

      :deep(.el-card__body) {
        padding: 20px;
      }
    }

    .overview-content {
      display: flex;
      align-items: center;

      .overview-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        &.revenue {
          background: linear-gradient(135deg, #2ec4b6, #1d9b8f);
          color: white;
        }

        &.orders {
          background: linear-gradient(135deg, #4361ee, #3f37c9);
          color: white;
        }

        &.users {
          background: linear-gradient(135deg, #ff9f1c, #e88e17);
          color: white;
        }

        &.rate {
          background: linear-gradient(135deg, #e71d36, #c2182b);
          color: white;
        }
      }

      .overview-info {
        flex: 1;

        .overview-number {
          font-size: 1.8rem;
          font-weight: 700;
          color: var(--dark-color);
          line-height: 1;
          margin-bottom: 4px;
        }

        .overview-label {
          font-size: 0.9rem;
          color: #666;
        }
      }
    }
  }

  .charts-row {
    margin-bottom: 24px;
  }

  .chart-card,
  .table-card {
    border: none;
    box-shadow: var(--box-shadow);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: var(--dark-color);
    }

    .chart-container {
      height: 300px;

      .chart {
        height: 100%;
        width: 100%;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .statistics-view {
    .overview-content {
      .overview-icon {
        width: 50px;
        height: 50px;
        margin-right: 12px;

        .el-icon {
          font-size: 24px;
        }
      }

      .overview-info {
        .overview-number {
          font-size: 1.4rem;
        }

        .overview-label {
          font-size: 0.8rem;
        }
      }
    }

    .chart-container {
      height: 250px;
    }

    .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
  }
}

@media (max-width: 480px) {
  .statistics-view {
    .overview-content {
      flex-direction: column;
      text-align: center;

      .overview-icon {
        margin-right: 0;
        margin-bottom: 12px;
      }
    }

    .chart-container {
      height: 200px;
    }
  }
}
</style>
