{"version": 3, "file": "panel-year-range.js", "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/panel-year-range.vue"], "sourcesContent": ["<template>\n  <div :class=\"panelKls\">\n    <div :class=\"ppNs.e('body-wrapper')\">\n      <slot name=\"sidebar\" :class=\"ppNs.e('sidebar')\" />\n      <div v-if=\"hasShortcuts\" :class=\"ppNs.e('sidebar')\">\n        <button\n          v-for=\"(shortcut, key) in shortcuts\"\n          :key=\"key\"\n          type=\"button\"\n          :class=\"ppNs.e('shortcut')\"\n          @click=\"handleShortcutClick(shortcut)\"\n        >\n          {{ shortcut.text }}\n        </button>\n      </div>\n      <div :class=\"ppNs.e('body')\">\n        <div :class=\"leftPanelKls.content\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              type=\"button\"\n              :class=\"leftPanelKls.arrowLeftBtn\"\n              @click=\"leftPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"leftPanelKls.arrowRightBtn\"\n              @click=\"leftNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ leftLabel }}</div>\n          </div>\n          <year-table\n            selection-mode=\"range\"\n            :date=\"leftDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n        <div :class=\"rightPanelKls.content\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"rightPanelKls.arrowLeftBtn\"\n              @click=\"rightPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              type=\"button\"\n              :class=\"rightPanelKls.arrowRightBtn\"\n              @click=\"rightNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ rightLabel }}</div>\n          </div>\n          <year-table\n            selection-mode=\"range\"\n            :date=\"rightDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref, toRef, useSlots, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { isArray } from '@element-plus/utils'\nimport { DArrowLeft, DArrowRight } from '@element-plus/icons-vue'\nimport ElIcon from '@element-plus/components/icon'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { PICKER_BASE_INJECTION_KEY } from '@element-plus/components/time-picker'\nimport {\n  panelYearRangeEmits,\n  panelYearRangeProps,\n} from '../props/panel-year-range'\nimport { useShortcut } from '../composables/use-shortcut'\nimport { useYearRangeHeader } from '../composables/use-year-range-header'\nimport { correctlyParseUserInput, isValidRange } from '../utils'\nimport {\n  ROOT_PICKER_INJECTION_KEY,\n  ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY,\n} from '../constants'\nimport YearTable from './basic-year-table.vue'\n\nimport type { Dayjs } from 'dayjs'\nimport type { RangeState } from '../props/shared'\n\ndefineOptions({\n  name: 'DatePickerYearRange',\n})\n\nconst props = defineProps(panelYearRangeProps)\nconst emit = defineEmits(panelYearRangeEmits)\n\nconst { lang } = useLocale()\nconst leftDate = ref(dayjs().locale(lang.value))\nconst rightDate = ref(leftDate.value.add(10, 'year'))\nconst { pickerNs: ppNs } = inject(ROOT_PICKER_INJECTION_KEY)!\nconst drpNs = useNamespace('date-range-picker')\nconst isDefaultFormat = inject(\n  ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY\n) as any\n\nconst hasShortcuts = computed(() => !!shortcuts.length)\n\nconst panelKls = computed(() => [\n  ppNs.b(),\n  drpNs.b(),\n  {\n    'has-sidebar': Boolean(useSlots().sidebar) || hasShortcuts.value,\n  },\n])\n\nconst leftPanelKls = computed(() => {\n  return {\n    content: [ppNs.e('content'), drpNs.e('content'), 'is-left'],\n    arrowLeftBtn: [ppNs.e('icon-btn'), 'd-arrow-left'],\n    arrowRightBtn: [\n      ppNs.e('icon-btn'),\n      { [ppNs.is('disabled')]: !enableYearArrow.value },\n      'd-arrow-right',\n    ],\n  }\n})\n\nconst rightPanelKls = computed(() => {\n  return {\n    content: [ppNs.e('content'), drpNs.e('content'), 'is-right'],\n    arrowLeftBtn: [\n      ppNs.e('icon-btn'),\n      { 'is-disabled': !enableYearArrow.value },\n      'd-arrow-left',\n    ],\n    arrowRightBtn: [ppNs.e('icon-btn'), 'd-arrow-right'],\n  }\n})\n\nconst handleShortcutClick = useShortcut(lang)\n\nconst {\n  leftPrevYear,\n  rightNextYear,\n  leftNextYear,\n  rightPrevYear,\n  leftLabel,\n  rightLabel,\n  leftYear,\n  rightYear,\n} = useYearRangeHeader({\n  unlinkPanels: toRef(props, 'unlinkPanels'),\n  leftDate,\n  rightDate,\n})\n\nconst enableYearArrow = computed(() => {\n  return props.unlinkPanels && rightYear.value > leftYear.value + 1\n})\n\nconst minDate = ref<Dayjs>()\nconst maxDate = ref<Dayjs>()\n\nconst rangeState = ref<RangeState>({\n  endDate: null,\n  selecting: false,\n})\n\nconst handleChangeRange = (val: RangeState) => {\n  rangeState.value = val\n}\n\ntype RangePickValue = {\n  minDate: Dayjs\n  maxDate: Dayjs\n}\nconst handleRangePick = (val: RangePickValue, close = true) => {\n  const minDate_ = val.minDate\n  const maxDate_ = val.maxDate\n  if (maxDate.value === maxDate_ && minDate.value === minDate_) {\n    return\n  }\n  emit('calendar-change', [minDate_.toDate(), maxDate_ && maxDate_.toDate()])\n  maxDate.value = maxDate_\n  minDate.value = minDate_\n\n  if (!close) return\n  handleConfirm()\n}\n\nconst handleConfirm = (visible = false) => {\n  if (isValidRange([minDate.value, maxDate.value])) {\n    emit('pick', [minDate.value, maxDate.value], visible)\n  }\n}\n\nconst onSelect = (selecting: boolean) => {\n  rangeState.value.selecting = selecting\n  if (!selecting) {\n    rangeState.value.endDate = null\n  }\n}\n\nconst pickerBase = inject(PICKER_BASE_INJECTION_KEY) as any\nconst { shortcuts, disabledDate } = pickerBase.props\nconst format = toRef(pickerBase.props, 'format')\nconst defaultValue = toRef(pickerBase.props, 'defaultValue')\nconst unit = 'year'\n\nconst getDefaultValue = () => {\n  let start: Dayjs\n  if (isArray(defaultValue.value)) {\n    const left = dayjs(defaultValue.value[0])\n    let right = dayjs(defaultValue.value[1])\n    if (!props.unlinkPanels) {\n      right = left.add(10, unit)\n    }\n    return [left, right]\n  } else if (defaultValue.value) {\n    start = dayjs(defaultValue.value)\n  } else {\n    start = dayjs()\n  }\n  start = start.locale(lang.value)\n  return [start, start.add(10, unit)]\n}\n\nwatch(\n  () => defaultValue.value,\n  (val) => {\n    if (val) {\n      const defaultArr = getDefaultValue()\n      leftDate.value = defaultArr[0]\n      rightDate.value = defaultArr[1]\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => props.parsedValue,\n  (newVal) => {\n    if (newVal && newVal.length === 2) {\n      minDate.value = newVal[0]\n      maxDate.value = newVal[1]\n      leftDate.value = minDate.value\n      if (props.unlinkPanels && maxDate.value) {\n        const minDateYear = minDate.value.year()\n        const maxDateYear = maxDate.value.year()\n        rightDate.value =\n          minDateYear === maxDateYear\n            ? maxDate.value.add(10, 'year')\n            : maxDate.value\n      } else {\n        rightDate.value = leftDate.value.add(10, 'year')\n      }\n    } else {\n      const defaultArr = getDefaultValue()\n      minDate.value = undefined\n      maxDate.value = undefined\n      leftDate.value = defaultArr[0]\n      rightDate.value = defaultArr[1]\n    }\n  },\n  { immediate: true }\n)\n\nconst parseUserInput = (value: Dayjs | Dayjs[]) => {\n  return correctlyParseUserInput(\n    value,\n    format.value,\n    lang.value,\n    isDefaultFormat\n  )\n}\n\nconst formatToString = (value: Dayjs[] | Dayjs) => {\n  return isArray(value)\n    ? value.map((day) => day.format(format.value))\n    : value.format(format.value)\n}\n\nconst isValidValue = (date: [Dayjs, Dayjs]) => {\n  return (\n    isValidRange(date) &&\n    (disabledDate\n      ? !disabledDate(date[0].toDate()) && !disabledDate(date[1].toDate())\n      : true)\n  )\n}\n\nconst handleClear = () => {\n  const defaultArr = getDefaultValue()\n  leftDate.value = defaultArr[0]\n  rightDate.value = defaultArr[1]\n  maxDate.value = undefined\n  minDate.value = undefined\n  emit('pick', null)\n}\n\nemit('set-picker-option', ['isValidValue', isValidValue])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['handleClear', handleClear])\n</script>\n"], "names": ["useLocale", "ref", "dayjs", "inject", "ROOT_PICKER_INJECTION_KEY", "useNamespace", "ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY", "computed", "useSlots", "useShortcut", "useYearRangeHeader", "toRef", "isValidRange", "PICKER_BASE_INJECTION_KEY", "isArray", "watch", "correctlyParseUserInput", "_openBlock", "_createElementBlock", "_unref", "_createElementVNode", "_normalizeClass", "_renderSlot"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;uCAqHc,CAAA;AAAA,EACZ,IAAM,EAAA,qBAAA;AACR;;;;;;;AAKA,IAAM,MAAA,EAAE,IAAK,EAAA,GAAIA,eAAU,EAAA,CAAA;AAC3B,IAAA,MAAM,WAAWC,OAAI,CAAAC,yBAAA,GAAQ,MAAO,CAAA,IAAA,CAAK,KAAK,CAAC,CAAA,CAAA;AAC/C,IAAA,MAAM,YAAYD,OAAI,CAAA,QAAA,CAAS,MAAM,GAAI,CAAA,EAAA,EAAI,MAAM,CAAC,CAAA,CAAA;AACpD,IAAA,MAAM,EAAE,QAAA,EAAU,IAAK,EAAA,GAAIE,WAAOC,mCAAyB,CAAA,CAAA;AAC3D,IAAM,MAAA,KAAA,GAAQC,qBAAa,mBAAmB,CAAA,CAAA;AAC9C,IAAA,MAAM,eAAkB,GAAAF,UAAA,CAAAG,qDAAA,CAAA,CAAA;AAAA,IACtB,MAAA,YAAA,GAAAC,YAAA,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IACF,MAAA,QAAA,GAAAA,YAAA,CAAA,MAAA;AAEA,MAAA,IAAM;AAEN,MAAM,KAAA,CAAA,CAAA,EAAA;AAA0B,MAC9B;AAAO,qBACC,EAAA,OAAA,CAAAC,YAAA,EAAA,CAAA,OAAA,CAAA,IAAA,YAAA,CAAA,KAAA;AAAA,OACR;AAAA,KAAA,CAAA,CAAA;AAC6D,IAC7D,MAAA,YAAA,GAAAD,YAAA,CAAA,MAAA;AAAA,MACD,OAAA;AAED,QAAM,OAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAe,SAAS,CAAM,EAAA,KAAA,CAAA,CAAA,CAAA,SAAA,CAAA,EAAA,SAAA,CAAA;AAClC,QAAO,YAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,cAAA,CAAA;AAAA,QACL,aAAU,EAAK;AAA2C,2BAC3C,CAAK;AAA6B,UAClC,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,UAAA,CAAA,GAAA,CAAA,eAAA,CAAA,KAAA,EAAA;AAAA,UACb,eAAiB;AAAA,SACjB;AAAgD,OAChD,CAAA;AAAA,KACF,CAAA,CAAA;AAAA,IACF,MAAA,aAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACD,OAAA;AAED,QAAM,OAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA,UAAyB,EAAM,KAAA,CAAA,CAAA,CAAA,SAAA,CAAA,EAAA,UAAA,CAAA;AACnC,QAAO,YAAA,EAAA;AAAA,UACL,IAAA,CAAA,CAAA,CAAS,UAAQ,CAAA;AAA0C,UAC7C,EAAA,aAAA,EAAA,CAAA,eAAA,CAAA,KAAA,EAAA;AAAA,UACZ,cAAiB;AAAA,SAAA;AACuB,QACxC,aAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,eAAA,CAAA;AAAA,OACF,CAAA;AAAA,KAAA,CAAA,CAAA;AACmD,IACrD,MAAA,mBAAA,GAAAE,uBAAA,CAAA,IAAA,CAAA,CAAA;AAAA,IACF,MAAC;AAED,MAAM,YAAA;AAEN,MAAM,aAAA;AAAA,MACJ,YAAA;AAAA,MACA,aAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,KACA,GAAAC,qCAAA,CAAA;AAAA,MACA,YAAA,EAAAC,SAAA,CAAA,KAAA,EAAA,cAAA,CAAA;AAAA,cACqB;AAAA,MACrB,SAAA;AAAyC,KACzC,CAAA,CAAA;AAAA,IACA,MAAA,eAAA,GAAAJ,YAAA,CAAA,MAAA;AAAA,MACD,OAAA,KAAA,CAAA,YAAA,IAAA,SAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,GAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,OAAa,GAAAN,OAAA,EAAA,CAAA;AAAmD,IAClE,MAAC,OAAA,GAAAA,OAAA,EAAA,CAAA;AAED,IAAA,MAAM,UAAU,GAAWA,OAAA,CAAA;AAC3B,MAAA;AAEA,MAAA;AAAmC,KAAA,CACjC,CAAS;AAAA,IAAA,MACE,iBAAA,GAAA,CAAA,GAAA,KAAA;AAAA,MACZ,UAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAED,KAAM,CAAA;AACJ,IAAA,MAAA,eAAmB,GAAA,CAAA,GAAA,EAAA,KAAA,GAAA,IAAA,KAAA;AAAA,MACrB,MAAA,QAAA,GAAA,GAAA,CAAA,OAAA,CAAA;AAMA,MAAA,MAAwB,QAAA,GAAA,GAAA,CAAA,OAAsB,CAAA;AAC5C,MAAA,IAAA,aAAiB,KAAI,QAAA,IAAA,OAAA,CAAA,KAAA,KAAA,QAAA,EAAA;AACrB,QAAA;AACA,OAAA;AACE,MAAA,IAAA,CAAA,iBAAA,EAAA,CAAA,QAAA,CAAA,MAAA,EAAA,EAAA,QAAA,IAAA,QAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA;AAAA,MACF,OAAA,CAAA,KAAA,GAAA,QAAA,CAAA;AACA,MAAK,OAAA,CAAA,KAAA,GAAA,QAAmB,CAAC;AACzB,MAAA,IAAA,CAAA,KAAgB;AAChB,QAAA,OAAgB;AAEhB,MAAA,aAAY,EAAA,CAAA;AACZ,KAAc,CAAA;AAAA,IAChB,MAAA,aAAA,GAAA,CAAA,OAAA,GAAA,KAAA,KAAA;AAEA,MAAM,IAAAW,kBAAA,CAAA,CAAA,OAAiB,CAAA,KAAU,EAAU,OAAA,CAAA,KAAA,CAAA,CAAA,EAAA;AACzC,QAAA,qBAAkB,CAAA,cAAuB,CAAA,KAAA,CAAA,EAAM,OAAG,CAAA,CAAA;AAChD,OAAA;AAAoD,KACtD,CAAA;AAAA,IACF,MAAA,QAAA,GAAA,CAAA,SAAA,KAAA;AAEA,MAAM,UAAA,CAAA,KAAY,CAAuB,SAAA,GAAA,SAAA,CAAA;AACvC,MAAA,IAAA,CAAA;AACA,QAAA,UAAgB,CAAA,KAAA,CAAA,OAAA,GAAA,IAAA,CAAA;AACd,OAAA;AAA2B,KAC7B,CAAA;AAAA,IACF,MAAA,UAAA,GAAAT,UAAA,CAAAU,qCAAA,CAAA,CAAA;AAEA,IAAM,MAAA,EAAA,SAAA,EAAa,YAAgC,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AACnD,IAAA,MAAM,MAAE,GAAAF,SAAwB,CAAA,UAAA,CAAA,KAAe,EAAA,QAAA,CAAA,CAAA;AAC/C,IAAA,MAAM,YAAS,GAAMA,SAAW,CAAA,UAAA,CAAA,KAAe,EAAA,cAAA,CAAA,CAAA;AAC/C,IAAA,MAAM,eAAe,GAAA,MAAiB;AAGtC,MAAA,IAAM;AACJ,MAAI,IAAAG,cAAA,CAAA,YAAA,CAAA,KAAA,CAAA,EAAA;AACJ,QAAI,MAAA,IAAQ,GAAaZ,yBAAA,CAAA,YAAQ,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAC/B,QAAA,IAAA,KAAa,GAAAA,yBAAA,CAAA,YAAmB,CAAA,KAAA,CAAA,CAAM,CAAC,CAAC,CAAA;AACxC,QAAA,IAAI,CAAQ,KAAA,CAAA,YAAmB,EAAA;AAC/B,UAAI,YAAqB,CAAA,GAAA,CAAA,EAAA,EAAA,IAAA,CAAA,CAAA;AACvB,SAAQ;AAAiB,QAC3B,OAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AACA,OAAO,MAAA,gBAAY,CAAA,KAAA,EAAA;AAAA,QACrB,KAAA,GAAAA,sCAA+B,CAAA,KAAA,CAAA,CAAA;AAC7B,OAAQ,MAAA;AAAwB,QAC3B,KAAA,GAAAA,yBAAA,EAAA,CAAA;AACL,OAAA;AAAc,MAChB,KAAA,GAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACA,MAAQ,OAAA,CAAA,KAAA,EAAa,KAAA,CAAA,GAAA,CAAA,EAAU,EAAA,IAAA,CAAA,CAAA,CAAA;AAC/B,KAAA,CAAA;AAAkC,IACpCa,SAAA,CAAA,MAAA,YAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAEA,MAAA,IAAA,GAAA,EAAA;AAAA,cACqB,UAAA,GAAA,eAAA,EAAA,CAAA;AAAA,QACV,QAAA,CAAA,KAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AACP,QAAA,SAAS,CAAA,KAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AACP,OAAA;AACA,KAAS,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAQ;AACjB,IAAUA,SAAA,CAAA,MAAA,KAAA,CAAA,oBAAoB,KAAA;AAAA,MAChC,IAAA,MAAA,IAAA,MAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AAAA,QACF,OAAA,CAAA,KAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QACE,aAAgB,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QACpB,QAAA,CAAA,KAAA,GAAA,OAAA,CAAA,KAAA,CAAA;AAEA,QAAA,IAAA,KAAA,CAAA,YAAA,IAAA,OAAA,CAAA,KAAA,EAAA;AAAA,gBACc,WAAA,GAAA,OAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA;AAAA,UACA,MAAA,WAAA,GAAA,OAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA;AACV,UAAI,SAAA,CAAA,KAAiB,GAAA,WAAA,KAAc,WAAA,GAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA,EAAA,MAAA,CAAA,GAAA,OAAA,CAAA,KAAA,CAAA;AACjC,SAAQ,MAAA;AACR,UAAQ,SAAA,CAAA,KAAA,WAAgB,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA,EAAA,MAAA,CAAA,CAAA;AACxB,SAAA;AACA,OAAI,MAAA;AACF,QAAM,MAAA,UAAA,GAAA,eAAsB,EAAA,CAAA;AAC5B,QAAM,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AACN,QAAU,OAAA,CAAA,KAAA,GAAA,KAAA,CACR;AAEY,QAAA,QACT,CAAA,KAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AACL,QAAA,SAAA,CAAA,KAAkB,GAAA,UAAA,CAAA,CAAA,CAAS,CAAM;AAAc,OACjD;AAAA,KAAA,EAAA,EACK,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AACL,IAAA,MAAA,uBAAmC,KAAA;AACnC,MAAA,OAAAC,6BAAgB,CAAA,KAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AAChB,KAAA,CAAA;AACA,IAAS,MAAA,cAAA,GAAQ,WAAW;AAC5B,MAAU,OAAAF,cAAA,CAAA,KAAA,CAAA,YAAmB,CAAC,CAAA,GAAA,KAAA,GAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KAChC,CAAA;AAAA,IACF,MAAA,YAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACA,yBAAkB,CAAA,IAAA,CAAA,KAAA,YAAA,GAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,KACpB,CAAA;AAEA,IAAM,MAAA,WAAA,GAAA,MAA6C;AACjD,MAAO,MAAA,UAAA,GAAA,eAAA,EAAA,CAAA;AAAA,MACL,QAAA,CAAA,KAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MAAA,SACO,CAAA,KAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MAAA,OACF,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAAA,MACL,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAAA,MACF,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAM,IAAA,CAAA,mBAAkB,EAA2B,CAAA,cAAA,EAAA,YAAA,CAAA,CAAA,CAAA;AACjD,IAAA,IAAA,CAAA,mBAAoB,EAChB,CAAM,gBAAa,EAAA,cAAW,CAAA,CAAA,CAAA;AACL,IAC/B,IAAA,CAAA,mBAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAEA,IAAM,IAAA,CAAA,mBAAyC,EAAA,CAAA,aAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AAC7C,IAAA,OAAA,CAAA,iBACmB;AAGb,MAER,OAAAG,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAEA,QAAA,yBAA0B,CAAAC,SAAA,CAAA,QAAA,CAAA,CAAA;AACxB,OAAA,EAAA;AACA,QAASC,8BAAoB;AAC7B,UAAU,KAAA,EAAAC,4BAAoB,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA;AAC9B,SAAA,EAAA;AACA,UAAAC,cAAgB,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA;AAChB,mBAAaD,kBAAI,CAAAF,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AAAA,WACnB,CAAA;AAEA,UAA0BA,SAAA,CAAA,YAAA,CAAA,IAAiBF,aAAA,EAAA,EAAAC,sBAAa,CAAA,KAAA,EAAA;AACxD,YAA0B,GAAA,EAAA,CAAA;AAC1B,YAA0B,KAAA,EAAAG,kBAAmB,CAAAF,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AAC7C,WAA0B,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}