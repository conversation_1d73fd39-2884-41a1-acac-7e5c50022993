<template>
  <div class="settings-view">
    <div class="page-header">
      <h1>系统设置</h1>
      <p>管理系统配置和参数</p>
    </div>

    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="16" :lg="16">
        <el-card class="settings-card">
          <template #header>
            <span>基本设置</span>
          </template>
          
          <el-form
            ref="settingsFormRef"
            :model="settingsForm"
            :rules="settingsRules"
            label-width="120px"
          >
            <el-form-item label="系统名称" prop="systemName">
              <el-input v-model="settingsForm.systemName" />
            </el-form-item>
            
            <el-form-item label="系统描述" prop="systemDescription">
              <el-input
                v-model="settingsForm.systemDescription"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            
            <el-form-item label="默认租赁天数" prop="defaultRentalDays">
              <el-input-number
                v-model="settingsForm.defaultRentalDays"
                :min="1"
                :max="365"
              />
            </el-form-item>
            
            <el-form-item label="最大租赁天数" prop="maxRentalDays">
              <el-input-number
                v-model="settingsForm.maxRentalDays"
                :min="1"
                :max="365"
              />
            </el-form-item>
            
            <el-form-item label="自动确认订单" prop="autoConfirmOrder">
              <el-switch v-model="settingsForm.autoConfirmOrder" />
            </el-form-item>
            
            <el-form-item label="允许用户注册" prop="allowUserRegistration">
              <el-switch v-model="settingsForm.allowUserRegistration" />
            </el-form-item>
            
            <el-form-item label="邮件通知" prop="emailNotification">
              <el-switch v-model="settingsForm.emailNotification" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleSave" :loading="loading">
                保存设置
              </el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="24" :md="8" :lg="8">
        <el-card class="info-card">
          <template #header>
            <span>系统信息</span>
          </template>
          
          <div class="system-info">
            <div class="info-item">
              <span class="label">系统版本:</span>
              <span class="value">v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="label">数据库版本:</span>
              <span class="value">MySQL 5.7</span>
            </div>
            <div class="info-item">
              <span class="label">运行时间:</span>
              <span class="value">15天 8小时</span>
            </div>
            <div class="info-item">
              <span class="label">总用户数:</span>
              <span class="value">89</span>
            </div>
            <div class="info-item">
              <span class="label">总商品数:</span>
              <span class="value">42</span>
            </div>
            <div class="info-item">
              <span class="label">总订单数:</span>
              <span class="value">156</span>
            </div>
          </div>
        </el-card>
        
        <el-card class="backup-card" style="margin-top: 20px;">
          <template #header>
            <span>数据备份</span>
          </template>
          
          <div class="backup-actions">
            <el-button type="primary" @click="handleBackup" :loading="backupLoading">
              <el-icon><Download /></el-icon>
              备份数据
            </el-button>
            <el-button type="warning" @click="handleRestore">
              <el-icon><Upload /></el-icon>
              恢复数据
            </el-button>
          </div>
          
          <div class="backup-history">
            <h4>备份历史</h4>
            <div class="backup-item" v-for="backup in backupHistory" :key="backup.id">
              <div class="backup-info">
                <div class="backup-name">{{ backup.name }}</div>
                <div class="backup-date">{{ backup.date }}</div>
              </div>
              <div class="backup-actions">
                <el-button size="small" type="text" @click="handleDownload(backup)">
                  下载
                </el-button>
                <el-button size="small" type="text" @click="handleDeleteBackup(backup)">
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { Download, Upload } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const backupLoading = ref(false)

// 表单引用
const settingsFormRef = ref<FormInstance>()

// 设置表单
const settingsForm = reactive({
  systemName: '商品租赁系统',
  systemDescription: '专业、便捷的商品租赁管理平台',
  defaultRentalDays: 7,
  maxRentalDays: 30,
  autoConfirmOrder: false,
  allowUserRegistration: true,
  emailNotification: true
})

// 备份历史
const backupHistory = ref([
  {
    id: 1,
    name: 'backup_2024_01_15.sql',
    date: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    name: 'backup_2024_01_10.sql',
    date: '2024-01-10 09:15:00'
  },
  {
    id: 3,
    name: 'backup_2024_01_05.sql',
    date: '2024-01-05 14:20:00'
  }
])

// 表单验证规则
const settingsRules: FormRules = {
  systemName: [
    { required: true, message: '请输入系统名称', trigger: 'blur' },
    { min: 2, max: 50, message: '系统名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  systemDescription: [
    { required: true, message: '请输入系统描述', trigger: 'blur' },
    { min: 10, max: 200, message: '系统描述长度在 10 到 200 个字符', trigger: 'blur' }
  ],
  defaultRentalDays: [
    { required: true, message: '请输入默认租赁天数', trigger: 'blur' }
  ],
  maxRentalDays: [
    { required: true, message: '请输入最大租赁天数', trigger: 'blur' }
  ]
}

// 保存设置
const handleSave = async () => {
  if (!settingsFormRef.value) return
  
  await settingsFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // TODO: 调用API保存设置
        await new Promise(resolve => setTimeout(resolve, 1000))
        ElMessage.success('设置保存成功')
      } catch (error) {
        ElMessage.error('保存设置失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 重置设置
const handleReset = () => {
  settingsFormRef.value?.resetFields()
}

// 备份数据
const handleBackup = async () => {
  backupLoading.value = true
  try {
    // TODO: 调用API备份数据
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const newBackup = {
      id: Date.now(),
      name: `backup_${new Date().toISOString().split('T')[0].replace(/-/g, '_')}.sql`,
      date: new Date().toLocaleString()
    }
    
    backupHistory.value.unshift(newBackup)
    ElMessage.success('数据备份成功')
  } catch (error) {
    ElMessage.error('数据备份失败')
  } finally {
    backupLoading.value = false
  }
}

// 恢复数据
const handleRestore = () => {
  ElMessageBox.confirm(
    '恢复数据将覆盖当前所有数据，确定要继续吗？',
    '确认恢复',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // TODO: 实现数据恢复功能
    ElMessage.info('数据恢复功能开发中')
  })
}

// 下载备份
const handleDownload = (backup: any) => {
  // TODO: 实现备份下载功能
  ElMessage.info(`下载备份: ${backup.name}`)
}

// 删除备份
const handleDeleteBackup = (backup: any) => {
  ElMessageBox.confirm(
    `确定要删除备份"${backup.name}"吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = backupHistory.value.findIndex(item => item.id === backup.id)
    if (index !== -1) {
      backupHistory.value.splice(index, 1)
      ElMessage.success('备份删除成功')
    }
  })
}

// 加载设置
const loadSettings = () => {
  // TODO: 调用API加载设置
  console.log('加载系统设置')
}

onMounted(() => {
  loadSettings()
})
</script>

<style lang="scss" scoped>
.settings-view {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px;
      color: var(--dark-color);
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #666;
    }
  }
  
  .settings-card,
  .info-card,
  .backup-card {
    border: none;
    box-shadow: var(--box-shadow);
  }
  
  .system-info {
    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }
      
      .label {
        color: #666;
        font-weight: 500;
      }
      
      .value {
        color: var(--dark-color);
        font-weight: 600;
      }
    }
  }
  
  .backup-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    
    .el-button {
      flex: 1;
    }
  }
  
  .backup-history {
    h4 {
      margin: 0 0 16px;
      color: var(--dark-color);
      font-size: 1rem;
    }
    
    .backup-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .backup-info {
        flex: 1;
        
        .backup-name {
          font-weight: 500;
          color: var(--dark-color);
          margin-bottom: 4px;
        }
        
        .backup-date {
          font-size: 0.85rem;
          color: #666;
        }
      }
      
      .backup-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .settings-view {
    .backup-actions {
      flex-direction: column;
    }
    
    .backup-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      
      .backup-actions {
        align-self: flex-end;
      }
    }
  }
}
</style>
