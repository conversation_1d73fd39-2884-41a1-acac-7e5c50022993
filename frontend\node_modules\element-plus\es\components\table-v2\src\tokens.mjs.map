{"version": 3, "file": "tokens.mjs", "sources": ["../../../../../../packages/components/table-v2/src/tokens.ts"], "sourcesContent": ["import type { Injection<PERSON><PERSON>, Ref } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\n\nexport type TableV2Context = {\n  isScrolling: Ref<boolean>\n  isResetting: Ref<boolean>\n  ns: UseNamespaceReturn\n}\n\nexport const TableV2InjectionKey: InjectionKey<TableV2Context> =\n  Symbol('tableV2')\n\nexport const TABLE_V2_GRID_INJECTION_KEY = 'tableV2GridScrollLeft'\n"], "names": [], "mappings": "AAAY,MAAC,mBAAmB,GAAG,MAAM,CAAC,SAAS,EAAE;AACzC,MAAC,2BAA2B,GAAG;;;;"}